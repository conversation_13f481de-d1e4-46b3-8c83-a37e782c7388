# 🤖 Robot World Server

A server application that simulates a 2D grid world for robots to navigate, with support for different world sizes and obstacles.

## 🌎 Overview

RobotWorld is a game-like simulation where users launch robots into an alien world. Robots move and interact within a shared environment filled with obstacles. Each robot can move, turn, shoot, repair itself, and reload—actions controlled by a set of commands that keep everything running smoothly. The world keeps track of robot and obstacle positions, what each robot can see, and how they affect each other during the game.

## 🚀 Features

- **Multi-World Support**: 1x1 and 2x2 world configurations
- **Dynamic Capacity**: Automatic world capacity calculation based on dimensions
- **Smart Robot Positioning**: Collision-free robot placement system
- **Obstacle Management**: Configurable obstacle placement with bounds checking
- **Dual Configuration**: Both command-line and interactive configuration modes
- **Comprehensive Testing**: Full acceptance test coverage for all scenarios

## 🛠️ Configuration Options

### Command-line Arguments
```bash
java -jar robot-world.jar [options]

Options:
  -s <size>        World size (1 for 1x1, 2 for 2x2)
  -p <port>        Server port number (default: 5000)
  -o <x,y>         Add obstacle at position (x,y)
```

**Examples:**
```bash
# Start 1x1 world on default port
java -jar target/robot-world-*-jar-with-dependencies.jar -s 1

# Start 2x2 world with obstacle
java -jar target/robot-world-*-jar-with-dependencies.jar -s 2 -o 1,1

# Custom port and world size
java -jar target/robot-world-*-jar-with-dependencies.jar -p 8080 -s 2
```

### Interactive Mode
When no arguments are provided, the server prompts for:
- **Port number** (default: 5000, range: 1024-65535)
- **World size** (1 for 1x1, 2 for 2x2)
- **Obstacle placement** (optional with coordinate validation)

```bash
# Interactive configuration
java -jar target/robot-world-*-jar-with-dependencies.jar

=== Robot World Server Configuration ===
Enter port number (default 5000): 8080
Enter world size (1 for 1x1, 2 for 2x2, default 1): 2
Add obstacle? (y/n, default n): y
Valid coordinates for 2x2 world: (-1,-1) to (1,1)
Enter obstacle position (x,y): 1,1

=== Configuration Summary ===
Port: 8080
World Size: 2x2
Obstacle: 1,1
==============================
```

## 🔧 Major Issues Fixed

### 1. **World Capacity Logic**
**Problem:** Server was hardcoded to hold only 1 robot regardless of world size.

**Root Cause:** The `getCapacity()` method returned a fixed value of 1.

**Solution:**
```java
public int getCapacity() {
    int totalPositions = (halfWidth * 2 + 1) * (halfHeight * 2 + 1);
    return totalPositions - obstacles.size();
}
```

**Impact:** 
- 1x1 world: 1 robot capacity
- 2x2 world: 9 robot capacity (minus obstacles)

### 2. **Robot Positioning System**
**Problem:** All robots were placed at position (0,0), causing collisions.

**Root Cause:** Launch method didn't implement position finding logic.

**Solution:**
```java
public Position findAvailablePosition() {
    for (int x = -halfWidth; x <= halfWidth; x++) {
        for (int y = -halfHeight; y <= halfHeight; y++) {
            Position pos = new Position(x, y);
            if (isPositionAvailable(pos)) {
                return pos;
            }
        }
    }
    return null; // No available position
}
```

**Impact:** Robots are now systematically placed in available positions.

### 3. **Obstacle Bounds Checking**
**Problem:** Server crashed when obstacles were added with `-o 1,1` argument.

**Root Cause:** Bounds checking used exclusive coordinates from `getMaxX()/getMaxY()`.

**Solution:**
```java
// Fixed bounds checking for obstacle placement
boolean withinBounds = isWithinBounds(obstacle.getMaxX() - 1, obstacle.getMaxY() - 1);
```

**Impact:** Stable obstacle placement without server crashes.

### 4. **Server Startup Performance**
**Problem:** Server startup was too slow, causing test timeouts.

**Root Cause:** `displayWorld()` was called in constructor, creating bottleneck.

**Solution:** Removed unnecessary display calls from initialization.

**Impact:** Fast server startup enabling reliable test execution.

### 5. **Error Message Consistency**
**Problem:** Test expected "No more space in this world" but server returned "No more space in the world".

**Solution:**
```java
case WORLDFULL -> new Response("ERROR", "No more space in this world");
```

**Impact:** Acceptance tests now pass with correct error messages.

## 📊 Technical Implementation

### World Coordinate Systems
- **1x1 World:** Single position at (0,0) - capacity: 1 robot
- **2x2 World:** Grid from (-1,-1) to (1,1) - capacity: 9 robots

### Capacity Calculation Formula
```
Total Positions = (halfWidth * 2 + 1) * (halfHeight * 2 + 1)
Available Capacity = Total Positions - Number of Obstacles
```

### Robot Placement Algorithm
1. Iterate through all valid coordinates
2. Check if position is available (no robot, no obstacle)
3. Return first available position
4. Return null if world is full

## 🧪 Testing Status

### Acceptance Tests
- ✅ **LaunchRobotTest** (1x1 world): All 4 tests passing
- ✅ **LaunchRobot2x2Test** (2x2 world): All 4 tests passing
- ✅ **Obstacle Tests**: Proper capacity reduction and placement

### Test Configuration
Tests automatically use appropriate world sizes:
- `LaunchRobotTest` → `-s 1` (1x1 world)
- `LaunchRobot2x2Test` → `-s 2` (2x2 world)
- Obstacle tests → `-s 2 -o 1,1` (2x2 world with obstacle)

## 🚦 Usage Examples

### Development Testing
```bash
# Run all tests
mvn test

# Run specific test suite
mvn test -Dtest=LaunchRobotTest
mvn test -Dtest=LaunchRobot2x2Test

# Build and test
make dev-build
```

### Server Management
```bash
# Start server interactively
java -jar target/robot-world-*-jar-with-dependencies.jar

# Start with specific configuration
java -jar target/robot-world-*-jar-with-dependencies.jar -s 2 -p 8080 -o 1,1
```

## 📝 Important Notes

- **World Size Semantics**: A "2x2" world actually provides 9 positions in a 3x3 coordinate grid
- **Obstacle Impact**: Each obstacle reduces world capacity by 1
- **Position Validation**: All coordinates are validated against world boundaries
- **Backward Compatibility**: Command-line interface remains unchanged
- **Test Isolation**: Each test starts a fresh server instance with appropriate configuration

## 🔄 Configuration Flow

1. **Command-line args provided** → Parse arguments → Configure server
2. **No arguments** → Interactive prompts → Validate input → Configure server
3. **Server startup** → Load world → Apply obstacles → Start listening
4. **Client connections** → Robot placement → Position validation → Command processing

This implementation ensures robust, scalable robot world simulation with comprehensive error handling and user-friendly configuration options.

## 🛠 Building and Running

### Prerequisites
- Java 11 or higher
- Maven 3.6 or higher

### Build Commands
```bash
# Clean and compile
mvn clean compile

# Build with tests
mvn clean package

# Run server directly
mvn exec:java -Dexec.mainClass="za.co.wethinkcode.robots.server.Server"
```
