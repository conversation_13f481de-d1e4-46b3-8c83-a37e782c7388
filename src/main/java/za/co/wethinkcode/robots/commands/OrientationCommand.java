package za.co.wethinkcode.robots.commands;

import za.co.wethinkcode.robots.Robot;
import za.co.wethinkcode.robots.server.Response;
import za.co.wethinkcode.robots.server.World;

public class OrientationCommand extends Command {
    public OrientationCommand(Robot robot) {
        super(robot, new String[]{});
    }

    @Override
    public String commandName() {
        return "orientation";
    }

    @Override
    public Response execute(World world) {
        return null;
    }
}