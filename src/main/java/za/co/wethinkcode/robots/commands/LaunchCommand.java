package za.co.wethinkcode.robots.commands;

import za.co.wethinkcode.robots.Robot;
import za.co.wethinkcode.robots.server.Response;
import za.co.wethinkcode.robots.server.World;

public class LaunchCommand extends Command {
    public LaunchCommand(Robot robot, String[] arguments) {
        super(robot, arguments);
    }

    @Override
    public String commandName() {
        return "launch";
    }

    @Override
    public Response execute(World world) {
        return null;
    }
}
