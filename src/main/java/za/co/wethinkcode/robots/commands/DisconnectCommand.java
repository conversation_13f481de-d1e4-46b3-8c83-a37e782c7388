package za.co.wethinkcode.robots.commands;

import za.co.wethinkcode.robots.server.Response;
import za.co.wethinkcode.robots.server.World;

public class DisconnectCommand extends Command {
    public DisconnectCommand() {
        super(null, new String[0]);
    }

    @Override
    public String commandName() {
        return "disconnect";
    }

    @Override
    public Response execute(World world) {
        return null;
    }
}