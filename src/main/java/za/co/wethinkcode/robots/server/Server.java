package za.co.wethinkcode.robots.server;
import za.co.wethinkcode.robots.handlers.ClientHandler;

import java.io.IOException;
import java.net.ServerSocket;
import java.net.Socket;
import java.util.Scanner;

/**
 * Main server class that accepts client connections and provides an admin console for server control.
 * Supports real-time robot monitoring, world state inspection, and graceful shutdown.
 */
public class Server {
    private static volatile boolean isRunning = true;
    private static ServerSocket serverSocket;

//    static {
//        new Recorder().logRun();
//    }

    public static void main(String[] args) {
        int portNumber;
        World world;

        try {
            System.out.println("Server starting with args: " + String.join(" ", args));
            ServerConfig config;

            // Check if command line arguments are provided
            if (args.length > 0) {
                config = ServerConfig.parseArgs(args);
            } else {
                // Use default configuration when no arguments provided
                config = new ServerConfig();
                System.out.println("Using default configuration");
            }

            portNumber = config.getPort();
            world = World.getInstance();

            // Apply configuration with command line overrides
            ConfigLoader configLoader = new ConfigLoader();
            Integer worldSize = config.getWorldSize() > 1 ? config.getWorldSize() : null;
            configLoader.applyConfigToWorld(world, "config.properties", worldSize, worldSize);

            // Add an obstacle if specified
            if (config.getObstaclePosition() != null) {
                addObstacleFromPosition(world, config.getObstaclePosition());
            }

            System.out.println("Configuration complete, starting server socket...");
        } catch (Exception e) {
            System.err.println("Error during server initialization: " + e);
            e.printStackTrace();
            System.exit(1);
            // This won't be reached but helps with compiler warnings
            return;
        }

        try {
            serverSocket = new ServerSocket(portNumber);
            System.out.println("Server started on port " + portNumber + ". Waiting for clients...");
            // Ensure the message is printed immediately
            System.out.flush();

            // launch admin console thread - disabled for testing
            // startAdminConsole(world);

            while (isRunning) {
                Socket clientSocket = serverSocket.accept();
                System.out.println("New client connected: " + clientSocket.getRemoteSocketAddress());
                new Thread(new ClientHandler(clientSocket, world)).start();
            }

        } catch (IOException e) {
            if (!isRunning) {
                System.out.println("Server shutdown.");
            } else {
                System.err.println("Got an error: " + e);
                e.printStackTrace();
            }
        } catch (Exception e) {
            System.err.println("Unexpected error: " + e);
            e.printStackTrace();
        }
    }
    private static void startAdminConsole(World world) {
        new Thread(() -> {
            Scanner scanner = new Scanner(System.in);
            while (isRunning) {
                try {
                    System.out.println("Valid Commands: 'quit', 'robots', 'dump', 'display'");
                    System.out.print("[Admin]: ");

                    // Check if input is available before trying to read
                    if (System.in.available() > 0) {
                        String input = scanner.nextLine().trim().toLowerCase();
                        switch (input) {
                            case "quit":
                                System.out.println("Shutting down server...");
                                shutdown();
                                break;
                            case "robots":
                                 System.out.println(world.getAllRobotsInfo());
                                break;
                            case "dump":
                                System.out.println(world.getFullWorldState());
                                break;
                            case "display":
                                world.displayWorld();
                                break;
                            default:
                                System.out.println("Unknown admin command.");
                        }
                    } else {
                        // Sleep briefly to avoid busy waiting
                        Thread.sleep(1000);
                    }
                } catch (Exception e) {
                    // If there's any issue with input, just continue
                    try {
                        Thread.sleep(1000);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                }
            }
        }, "AdminConsole").start();
    }


    public static void shutdown() {
        isRunning = false;
        try {
            serverSocket.close();
        } catch (IOException e) {
            System.out.println("Got an error when shutting down: " + e);
        }
    }

    private static void addObstacleFromPosition(World world, String position) {
        try {
            String[] coords = position.split(",");
            if (coords.length == 2) {
                int x = Integer.parseInt(coords[0]);
                int y = Integer.parseInt(coords[1]);
                Obstacle obstacle = new Obstacle(ObstacleType.MOUNTAIN, x, y, 1, 1);
                world.addObstacle(obstacle);
                System.out.println("Added obstacle at position [" + x + "," + y + "]");
            }
        } catch (Exception e) {
            System.err.println("Invalid obstacle position format: " + position);
        }
    }

    // Getting the server configuration through interactive prompts
    private static ServerConfig getInteractiveConfiguration() {
        Scanner scanner = new Scanner(System.in);

        // Get port number
        int port = getPortFromUser(scanner);

        // Get world size
        int worldSize = getWorldSizeFromUser(scanner);

        // Get obstacle position (optional)
        String obstaclePosition = getObstacleFromUser(scanner, worldSize);

        System.out.println("\n=== Configuration Summary ===");
        System.out.println("Port: " + port);
        System.out.println("World Size: " + worldSize + "x" + worldSize);
        if (obstaclePosition != null) {
            System.out.println("Obstacle: " + obstaclePosition);
        } else {
            System.out.println("Obstacle: None");
        }
        System.out.println("==============================\n");

        // Create ServerConfig with the collected values
        ServerConfig config = new ServerConfig();
        config.setPort(port);
        config.setWorldSize(worldSize);
        config.setObstaclePosition(obstaclePosition);
        return config;
    }

    private static int getPortFromUser(Scanner scanner) {
        while (true) {
            System.out.print("Enter port number (default 5000): ");
            String input = scanner.nextLine().trim();

            if (input.isEmpty()) {
                return 5000; // default port
            }

            try {
                int port = Integer.parseInt(input);
                if (port >= 1024 && port <= 65535) {
                    return port;
                } else {
                    System.out.println("Port must be between 1024 and 65535. Please try again.");
                }
            } catch (NumberFormatException e) {
                System.out.println("Invalid port number. Please enter a valid number.");
            }
        }
    }

    private static int getWorldSizeFromUser(Scanner scanner) {
        while (true) {
            System.out.print("Enter world size (1 for 1x1, 2 for 2x2, default 1): ");
            String input = scanner.nextLine().trim();

            if (input.isEmpty()) {
                // default world size
                return 1;
            }

            try {
                int size = Integer.parseInt(input);
                if (size == 1 || size == 2) {
                    return size;
                } else {
                    System.out.println("World size must be 1 or 2. Please try again.");
                }
            } catch (NumberFormatException e) {
                System.out.println("Invalid world size. Please enter 1 or 2.");
            }
        }
    }

    private static String getObstacleFromUser(Scanner scanner, int worldSize) {
        System.out.print("Add obstacle? (y/n, default n): ");
        String addObstacle = scanner.nextLine().trim().toLowerCase();

        if (!addObstacle.equals("y") && !addObstacle.equals("yes")) {
            // No obstacle
            return null;
        }

        // Show valid coordinates based on world size
        if (worldSize == 1) {
            System.out.println("Valid coordinates for 1x1 world: (0,0)");
        } else if (worldSize == 2) {
            System.out.println("Valid coordinates for 2x2 world: (-1,-1) to (1,1)");
        }

        while (true) {
            System.out.print("Enter obstacle position (x,y): ");
            String input = scanner.nextLine().trim();

            if (input.isEmpty()) {
                // No obstacle
                return null;
            }

            try {
                String[] parts = input.split(",");
                if (parts.length == 2) {
                    int x = Integer.parseInt(parts[0].trim());
                    int y = Integer.parseInt(parts[1].trim());

                    // Validate coordinates based on world size
                    boolean valid = false;
                    if (worldSize == 1) {
                        valid = (x == 0 && y == 0);
                    } else if (worldSize == 2) {
                        valid = (x >= -1 && x <= 1 && y >= -1 && y <= 1);
                    }

                    if (valid) {
                        return x + "," + y;
                    } else {
                        System.out.println("Invalid coordinates for " + worldSize + "x" + worldSize + " world. Please try again.");
                    }
                } else {
                    System.out.println("Invalid format. Please use x,y format (e.g., 1,1).");
                }
            } catch (NumberFormatException e) {
                System.out.println("Invalid coordinates. Please enter numbers only.");
            }
        }
    }
}
