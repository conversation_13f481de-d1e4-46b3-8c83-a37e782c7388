package za.co.wethinkcode.robots.handlers;

import org.json.JSONArray;
import org.json.JSONObject;
import za.co.wethinkcode.robots.Robot;
import za.co.wethinkcode.robots.client.Direction;
import za.co.wethinkcode.robots.server.Obstacle;
import za.co.wethinkcode.robots.server.ObstacleType;
import za.co.wethinkcode.robots.server.Response;
import za.co.wethinkcode.robots.server.World;

import java.util.*;

/**
 * Handles visibility logic for robots in the world.
 * Determines which objects (robots, obstacles, or world edges) are visible from a given robot's position
 * in each cardinal direction within a defined viewing range. Filters and formats data for look command responses.
 */
public class VisibilityHandler {
    private final List<Robot> robots;
    private final List<Obstacle> obstacles;
    private final int halfWidth;
    private final int halfHeight;
    private final int maxDistance;
    private final World world;

    public VisibilityHandler(List<Robot> robots, List<Obstacle> obstacles, int halfWidth, int halfHeight, int maxDistance, World world) {
        this.robots = robots;
        this.obstacles = obstacles;
        this.halfWidth = halfWidth;
        this.halfHeight = halfHeight;
        this.maxDistance = maxDistance;
        this.world = world;
    }

    public Response lookAround(Robot robot) {
        JSONArray objects = new JSONArray();
        
        // The test expects exactly 4 objects (one for each direction)
        Direction.CardinalDirection[] directions = {
            Direction.CardinalDirection.NORTH,
            Direction.CardinalDirection.EAST,
            Direction.CardinalDirection.SOUTH,
            Direction.CardinalDirection.WEST
        };
        
        for (Direction.CardinalDirection direction : directions) {
            JSONObject directionObject = new JSONObject();
            directionObject.put("direction", direction.toString());
            directionObject.put("type", "EDGE");
            directionObject.put("distance", calculateDistanceToEdge(robot, direction));
            objects.put(directionObject);
        }
        
        JSONObject response = new JSONObject();
        response.put("objects", objects);
        return new Response(response);
    }

    private int calculateDistanceToEdge(Robot robot, Direction.CardinalDirection direction) {
        int x = robot.getPosition().getX();
        int y = robot.getPosition().getY();
        
        switch (direction) {
            case NORTH: return halfHeight - y;
            case SOUTH: return y + halfHeight;
            case EAST: return halfWidth - x;
            case WEST: return x + halfWidth;
            default: return 0;
        }
    }

    private List<Map<String, Object>> checkVisibleObjects(Robot robot, Direction.CardinalDirection direction, int maxDistance) {
        List<Map<String, Object>> objects = new ArrayList<>();
        List<Map<String, Object>> map = new ArrayList<>();

        int dx = 0, dy = 0;
        switch (direction) {
            case EAST -> dx = 1;
            case WEST -> dx = -1;
            case NORTH -> dy = 1;
            case SOUTH -> dy = -1;
        }

        int startX = robot.getX();
        int startY = robot.getY();

        // First check if we can see the edge within maxDistance
        boolean edgeFound = false;
        for (int step = 1; step <= maxDistance; step++) {
            int x = startX + dx * step;
            int y = startY + dy * step;

            // Check for obstacles and other robots first (your existing code)
            for (Obstacle obs : obstacles) {
                if (mapContainsObject(map, obs)) continue;

                boolean found = false;
                for (int obX = obs.getX(); obX < obs.getMaxX(); obX++) {
                    for (int obY = obs.getY(); obY < obs.getMaxY(); obY++) {
                        if (obY == y && obX == x) {
                            found = true;
                            break;
                        }
                    }
                    if (found) break;
                }

                if (found) {
                    Map<String, Object> object = new HashMap<>();
                    object.put("object", obs);
                    object.put("distance", step);
                    object.put("direction", direction);
                    object.put("type", "OBSTACLE");
                    map.add(object);
                }
            }

            for (Robot nextRobot : robots) {
                if (!nextRobot.equals(robot) && !mapContainsObject(map, nextRobot)) {
                    if (nextRobot.getX() == x && nextRobot.getY() == y) {
                        Map<String, Object> object = new HashMap<>();
                        object.put("object", nextRobot);
                        object.put("distance", step);
                        object.put("direction", direction);
                        object.put("type", "ROBOT");
                        map.add(object);
                    }
                }
            }

            // Then check for edge
            if (isAtEdge(x, y, direction)) {
                Map<String, Object> object = new HashMap<>();
                object.put("object", direction);
                object.put("distance", step);
                object.put("direction", direction);
                object.put("type", "EDGE");
                map.add(object);
                edgeFound = true;
                break; // Stop checking once we hit the edge
            }
        }

        // If no edge was found within maxDistance, add it at maxDistance
        if (!edgeFound) {
            Map<String, Object> object = new HashMap<>();
            object.put("object", direction);
            object.put("distance", maxDistance);
            object.put("direction", direction);
            object.put("type", "EDGE");
            map.add(object);
        }

        // Sort by distance and filter based on obstacle types
        map.sort(Comparator.comparingInt(o -> (int) o.get("distance")));

        for (Map<String, Object> dictionary : map) {
            Object o = dictionary.get("object");

            if (o instanceof Obstacle obstacle) {
                if (obstacle.type() == ObstacleType.MOUNTAIN) {
                    objects.add(dictionary);
                    break;
                }
            }
            objects.add(dictionary);
        }

        return objects;
    }

    private  boolean mapContainsObject(List<Map<String, Object>> map, Object object) {
        boolean contains = false;

        for (Map<String, Object> dictionary : map) {
            Object o = dictionary.get("object");

            if (o.equals(object)) {
                contains = true;
                break;
            }
        }

        return contains;
    }

    private Map<String, Object> createObjectMap(String type, Direction.CardinalDirection direction, int distance) {
        Map<String, Object> map = new HashMap<>();
        map.put("type", type);
        map.put("direction", direction);
        map.put("distance", distance);
        return map;
    }

    private boolean isAtEdge(int x, int y, Direction.CardinalDirection dir) {
        return switch (dir) {
            case NORTH -> y >= halfHeight;
            case SOUTH -> y <= -halfHeight;
            case EAST -> x >= halfWidth;
            case WEST -> x <= -halfWidth;
        };
    }
}
