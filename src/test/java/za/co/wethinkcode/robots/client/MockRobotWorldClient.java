package za.co.wethinkcode.robots.client;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;

/**
 * A mock implementation of RobotWorldClient for testing.
 * Returns predefined responses for specific requests without connecting to a server.
 */
public class MockRobotWorldClient implements RobotWorldClient {
    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();
    private boolean connected = false;

    @Override
    public void connect(String ipAddress, int port) {
        connected = true;
    }

    @Override
    public boolean isConnected() {
        return connected;
    }

    @Override
    public void disconnect() {
        connected = false;
    }

    @Override
    public JsonNode sendRequest(String requestJsonString) {
        try {
            JsonNode requestJson = OBJECT_MAPPER.readTree(requestJsonString);
            String command = requestJson.get("command").asText();
            String robot = requestJson.get("robot").asText();

            if (command.equals("launch")) {
                // Return a successful launch response
                ObjectNode response = OBJECT_MAPPER.createObjectNode();
                response.put("result", "OK");
                
                ObjectNode data = OBJECT_MAPPER.createObjectNode();
                data.put("position", "[0, 0]");
                data.put("visibility", 10);
                data.put("reload", 3);
                data.put("repair", 5);
                data.put("shields", 10);
                
                response.set("data", data);
                
                ObjectNode state = OBJECT_MAPPER.createObjectNode();
                state.put("position", "[0, 0]");
                state.put("direction", "NORTH");
                state.put("shields", 10);
                state.put("shots", 3);
                state.put("status", "NORMAL");
                
                response.set("state", state);
                
                return response;
            } else if (command.equals("state")) {
                if (robot.equals("HAL")) {
                    // Return a successful state response for an existing robot
                    ObjectNode response = OBJECT_MAPPER.createObjectNode();
                    response.put("result", "OK");
                    
                    ObjectNode state = OBJECT_MAPPER.createObjectNode();
                    state.put("position", "[0, 0]");
                    state.put("direction", "NORTH");
                    state.put("shields", 10);
                    state.put("shots", 3);
                    state.put("status", "NORMAL");
                    
                    response.set("state", state);
                    
                    return response;
                } else {
                    // Return an error response for a non-existent robot
                    ObjectNode response = OBJECT_MAPPER.createObjectNode();
                    response.put("result", "ERROR");
                    
                    ObjectNode data = OBJECT_MAPPER.createObjectNode();
                    data.put("message", "Could not find robot: " + robot);
                    
                    response.set("data", data);
                    
                    return response;
                }
            }
            
            // Default response for unhandled commands
            ObjectNode response = OBJECT_MAPPER.createObjectNode();
            response.put("result", "ERROR");
            response.put("message", "Unsupported command");
            return response;
            
        } catch (JsonProcessingException e) {
            throw new RuntimeException("Error parsing request JSON", e);
        }
    }

    @Override
    public String sendRequestAsString(String requestString) {
        return sendRequest(requestString).toString();
    }
}