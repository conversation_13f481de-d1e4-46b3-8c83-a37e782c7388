package za.co.wethinkcode.robots.server;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

public class ServerConfigTest {
    
    @Test
    public void testDefaultValues() {
        ServerConfig config = ServerConfig.parseArgs(new String[]{});
        
        assertEquals(5000, config.getPort());
        assertEquals(1, config.getWorldSize());
        assertNull(config.getObstaclePosition());
    }
    
    @Test
    public void testPortArgument() {
        ServerConfig config = ServerConfig.parseArgs(new String[]{"-p", "8080"});
        
        assertEquals(8080, config.getPort());
        assertEquals(1, config.getWorldSize());
        assertNull(config.getObstaclePosition());
    }
    
    @Test
    public void testWorldSizeArgument() {
        ServerConfig config = ServerConfig.parseArgs(new String[]{"-s", "100"});
        
        assertEquals(5000, config.getPort());
        assertEquals(100, config.getWorldSize());
        assertNull(config.getObstaclePosition());
    }
    
    @Test
    public void testObstacleArgument() {
        ServerConfig config = ServerConfig.parseArgs(new String[]{"-o", "10,5"});
        
        assertEquals(5000, config.getPort());
        assertEquals(1, config.getWorldSize());
        assertEquals("10,5", config.getObstaclePosition());
    }
    
    @Test
    public void testAllArguments() {
        ServerConfig config = ServerConfig.parseArgs(new String[]{"-p", "8080", "-s", "100", "-o", "10,5"});
        
        assertEquals(8080, config.getPort());
        assertEquals(100, config.getWorldSize());
        assertEquals("10,5", config.getObstaclePosition());
    }
}