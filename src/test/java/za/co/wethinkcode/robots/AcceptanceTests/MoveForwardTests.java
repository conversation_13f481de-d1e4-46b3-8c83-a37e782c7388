package za.co.wethinkcode.robots.AcceptanceTests;

import com.fasterxml.jackson.databind.JsonNode;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import za.co.wethinkcode.robots.client.RobotWorldClient;
import za.co.wethinkcode.robots.client.RobotWorldJsonClient;
import za.co.wethinkcode.robots.server.ConfigLoader;

import java.io.IOException;
import java.net.Socket;

import static org.junit.jupiter.api.Assertions.*;

public class MoveForwardTests {
    private final static int DEFAULT_PORT = 5000;
    private final static String DEFAULT_IP = "localhost";
    private final RobotWorldClient serverClient = new RobotWorldJsonClient();
    private final ConfigLoader configLoader = new ConfigLoader();
    private String url;
    private Process serverProcess;

    @BeforeEach
    void connectToServer() throws IOException {
        startServer("-s", "1");
        waitForServerToStart(DEFAULT_IP, DEFAULT_PORT, 10000);
        serverClient.connect(DEFAULT_IP, DEFAULT_PORT);
    }

    @AfterEach
    void disconnectFromServer(){
        try{
            if (serverClient.isConnected()) {
                serverClient.disconnect();
            }
        } catch (RuntimeException e) {
            // Server may have already disconnected - this is acceptable
            System.out.println("Server already disconnected: " + e.getMessage());
        }

        // Kill server process
        if (serverProcess != null && serverProcess.isAlive()) {
            serverProcess.destroyForcibly();
            try {
                serverProcess.waitFor();
                Thread.sleep(500);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }
    }

    private void startServer(String... additionalArgs) throws IOException {
        //Load server url from a properties file
        url = configLoader.loadServerUrl("serverConfig.properties");

        ProcessBuilder pb = new ProcessBuilder();
        pb.command().add("java");
        pb.command().add("-jar");
        pb.command().add(url);

        // Add any additional arguments
        for (String arg : additionalArgs) {
            pb.command().add(arg);
        }

        serverProcess = pb.start();
    }

    private void waitForServerToStart(String host, int port, long timeoutMs) {
        long startTime = System.currentTimeMillis();
        // Start with 100ms wait
        long waitTime = 100;

        while (System.currentTimeMillis() - startTime < timeoutMs) {
            try (Socket testSocket = new Socket(host, port)) {
                // If we can connect, the server is ready
                System.out.println("Server is ready for connections");
                return;
            } catch (IOException e) {
                // When the server not ready yet, wait and retry
                try {
                    Thread.sleep(waitTime);
                    // Exponential backoff, max 1 second
                    waitTime = Math.min(waitTime * 2, 1000);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    throw new RuntimeException("Interrupted while waiting for server to start", ie);
                }
            }
        }

        throw new RuntimeException("Server failed to start within " + timeoutMs + "ms");
    }



    @Test
    void moveForwardAtTheEdgeOfTheWorld(){
        //Given that I am connected to a running Robot world server,
        //And the world is of size 1x1 with no obstacles or pits
        assertTrue(serverClient.isConnected());

        //And a robot called "HAL" is already connected and launched
        String launchRequest = "{" +
                "\"robot\": \"HAL\"," +
                "\"command\": \"launch\"," +
                "\"arguments\": [\"sniper\",\"5\",\"5\"]" +
                "}";
        JsonNode response = serverClient.sendRequest(launchRequest);
        assertEquals("OK", response.get("result").asText());

        //When I send a command for 'HAL' to move forward 5 steps
        String moveRequest = "{" +
                "\"robot\": \"HAL\"," +
                "\"command\": \"forward\"," +
                "\"arguments\": [\"5\"]" +
                "}";
        JsonNode moveResponse = serverClient.sendRequest(moveRequest);

        //Then I should get an 'OK' response with the message 'At the NORTH edge'
        assertEquals("OK", moveResponse.get("result").asText());
        assertTrue(moveResponse.get("data").get("message").asText().contains("At the NORTH edge"));

        //And the position information should be at co-ordinates[0,0]
        assertEquals(0,response.get("data").get("position").get(0).asInt());
        assertEquals(0,response.get("data").get("position").get(1).asInt());

    }
}
