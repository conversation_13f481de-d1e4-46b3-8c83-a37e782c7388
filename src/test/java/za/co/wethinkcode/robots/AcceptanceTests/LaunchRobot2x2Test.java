package za.co.wethinkcode.robots.AcceptanceTests;

import com.fasterxml.jackson.databind.JsonNode;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import za.co.wethinkcode.robots.client.RobotWorldClient;
import za.co.wethinkcode.robots.client.RobotWorldJsonClient;
import za.co.wethinkcode.robots.server.ConfigLoader;
import za.co.wethinkcode.robots.server.World;
import za.co.wethinkcode.robots.server.Obstacle;
import za.co.wethinkcode.robots.server.ObstacleType;

import java.io.IOException;
import java.net.Socket;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;

public class LaunchRobot2x2Test {
    private final static int DEFAULT_PORT = 5000;
    private final static String DEFAULT_IP = "localhost";
    private final RobotWorldClient serverClient = new RobotWorldJsonClient();
    private final ConfigLoader configLoader = new ConfigLoader();
    private String url;
    private Process serverProcess;


    @BeforeEach
    void connectToServer() throws IOException {
        //Start server with 2x2 world size
        startServer("-s", "2");
        waitForServerToStart(DEFAULT_IP, DEFAULT_PORT, 10000);
        serverClient.connect(DEFAULT_IP, DEFAULT_PORT);
    }

    @AfterEach
    void disconnectAndStopServer() {
        try {
            if (serverClient.isConnected()) {
                serverClient.disconnect();
            }
        } catch (RuntimeException e) {
            // Server may have already disconnected
            System.out.println("Server already disconnected: " + e.getMessage());
        }
        // Kill server process
        if (serverProcess != null && serverProcess.isAlive()) {
            serverProcess.destroyForcibly();
            try {
                serverProcess.waitFor();
                Thread.sleep(500);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }
    }

    // Helper methods to reduce duplication
    private void startServer(String... additionalArgs) throws IOException {
        // Load server url from a properties file
        url = configLoader.loadServerUrl("serverConfig.properties");

        ProcessBuilder pb = new ProcessBuilder();
        pb.command().add("java");
        pb.command().add("-jar");
        pb.command().add(url);

        // Add any additional arguments (like -s 2)
        for (String arg : additionalArgs) {
            pb.command().add(arg);
        }

        serverProcess = pb.start();
        System.out.println("Started reference server with args: " + String.join(" ", additionalArgs));
    }


    /**
     * Waits for the server to start and become available for connections.
     * Uses a retry mechanism with exponential backoff.
     *
     * @param host The host to connect to
     * @param port The port to connect to
     * @param timeoutMs Maximum time to wait in milliseconds
     * @throws RuntimeException if server doesn't start within the timeout
     */
    private void waitForServerToStart(String host, int port, long timeoutMs) {
        long startTime = System.currentTimeMillis();
        long waitTime = 100; // Start with 100ms wait

        while (System.currentTimeMillis() - startTime < timeoutMs) {
            try (Socket testSocket = new Socket(host, port)) {
                // If we can connect, the server is ready
                System.out.println("Server is ready for connections");
                return;
            } catch (IOException e) {
                // Server is not ready yet, wait and retry
                try {
                    Thread.sleep(waitTime);
                    waitTime = Math.min(waitTime * 2, 1000);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    throw new RuntimeException("Interrupted while waiting for server to start", ie);
                }
            }
        }

        throw new RuntimeException("Server failed to start within " + timeoutMs + "ms");
    }

    private void killProcessOnPort(int port) {
        try {
            if (System.getProperty("os.name").toLowerCase().contains("windows")) {
                // Use netstat to find PID and task kill to terminate
                ProcessBuilder pb = new ProcessBuilder("cmd", "/c",
                        "for /f \"tokens=5\" %a in ('netstat -ano ^| findstr :" + port + "') do taskkill /PID %a /F >nul 2>&1");
                Process killProcess = pb.start();
                killProcess.waitFor(3, TimeUnit.SECONDS);

                // Additional cleanup - wait a bit for port to be released
                Thread.sleep(500);
            } else {
                //cleanup
                ProcessBuilder pb = new ProcessBuilder("bash", "-c",
                        "lsof -ti:" + port + " | xargs kill -9");
                Process killProcess = pb.start();
                killProcess.waitFor(3, TimeUnit.SECONDS);
                Thread.sleep(500);
            }
        } catch (Exception e) {
            System.out.println("Could not kill process on port " + port + ": " + e.getMessage());
        }
    }

    @Test
    void canLaunchAnotherRobotInLargerWorld() {
        // Given that I am connected to a Robot Worlds server
        // And the world is of size 2x2
        assertTrue(serverClient.isConnected());

        // And robot "HAL" has already been launched into the world
        String launchHAL = "{" +
                "\"robot\": \"HAL\"," +
                "\"command\": \"launch\"," +
                "\"arguments\": [\"shooter\",\"5\",\"5\"]" +
                "}";
        JsonNode responseHAL = serverClient.sendRequest(launchHAL);
        assertEquals("OK", responseHAL.get("result").asText());

        // When I launch the robot "R2D2" into the world
        String launchR2D2 = "{" +
                "\"robot\": \"R2D2\"," +
                "\"command\": \"launch\"," +
                "\"arguments\": [\"shooter\",\"5\",\"5\"]" +
                "}";
        JsonNode responseR2D2 = serverClient.sendRequest(launchR2D2);

        // Then the launch should be successful
        assertEquals("OK", responseR2D2.get("result").asText());

        // And a position should be returned
        assertNotNull(responseR2D2.get("data"));
        assertNotNull(responseR2D2.get("data").get("position"));
        assertTrue(responseR2D2.get("data").get("position").isArray());
        assertEquals(2, responseR2D2.get("data").get("position").size());
    }

    @Test
    void worldWithoutObstaclesIsFull() {
        // Given that I am connected to a Robot Worlds server
        // And the world is of size 2x2
        assertTrue(serverClient.isConnected());

        int successfulLaunches = 0;
        int failedLaunches = 0;

        // When I attempt to launch 10 robots with unique names
        for (int i = 1; i <= 10; i++) {
            String robotName = "Robot" + i;
            String launchRequest = "{" +
                    "\"robot\": \"" + robotName + "\"," +
                    "\"command\": \"launch\"," +
                    "\"arguments\": [\"shooter\",\"5\",\"5\"]" +
                    "}";

            JsonNode response = serverClient.sendRequest(launchRequest);

            // Check if launch was successful
            assertNotNull(response.get("result"), "Response should have a result field for " + robotName);

            if ("OK".equals(response.get("result").asText())) {
                successfulLaunches++;

                // Verify response has data with position
                assertNotNull(response.get("data"), "Response should have data field for " + robotName);
                assertNotNull(response.get("data").get("position"),
                        "Response should have position data for " + robotName);

                // Get and display robot position
                JsonNode position = response.get("data").get("position");
                int x = position.get(0).asInt();
                int y = position.get(1).asInt();

                // Verify position is within 2x2 world bounds (coordinates from -1 to 1)
                assertTrue(x >= -1 && x <= 1, "X coordinate should be within 2x2 world bounds for " + robotName);
                assertTrue(y >= -1 && y <= 1, "Y coordinate should be within 2x2 world bounds for " + robotName);

            } else {
                failedLaunches++;
                System.out.println("Robot " + robotName + " failed to launch: " +
                        response.get("data").get("message").asText());
            }
        }

        // Then exactly 9 robots should launch successfully (2x2 world capacity)
        assertEquals(9, successfulLaunches,
                "Should have launched exactly 9 robots in 2x2 world, but launched " + successfulLaunches);

        // And exactly 1 robot should fail to launch (world full)
        assertEquals(1, failedLaunches,
                "Should have 1 failed launch due to world being full, but had " + failedLaunches + " failures");
    }

    @Nested
    class WorldWithObstacleTests {

        @BeforeEach
        void setupObstacleWorld() throws IOException {
            // Disconnect from the current server if connected
            if (serverClient.isConnected()) {
                serverClient.disconnect();
            }

            // Kill existing server process
            if (serverProcess != null && serverProcess.isAlive()) {
                serverProcess.destroyForcibly();
                try {
                    serverProcess.waitFor();
                    Thread.sleep(500);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
            }

            // Start server with obstacle configuration
            startServer("-s", "2", "-o", "1,1");
            // Wait up to 10 seconds
            waitForServerToStart(DEFAULT_IP, DEFAULT_PORT, 10000);
            serverClient.connect(DEFAULT_IP, DEFAULT_PORT);
        }

        @AfterEach
        void cleanupObstacleWorld() {
            // Disconnect from server
            if (serverClient.isConnected()) {
                try {
                    serverClient.disconnect();
                } catch (RuntimeException e) {
                    // The Server may have already disconnected/crashed
                    System.out.println("Server already disconnected: " + e.getMessage());
                }
            }

            // Kill the server process more aggressively
            if (serverProcess != null && serverProcess.isAlive()) {
                serverProcess.destroyForcibly();
                try {
                    boolean terminated = serverProcess.waitFor(5, TimeUnit.SECONDS);
                    if (!terminated) {
                        // Force kill using system commands if Java process cleanup fails
                        killProcessOnPort(DEFAULT_PORT);
                    }
                    Thread.sleep(1000);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
            }

            // Ensure port is free
            killProcessOnPort(DEFAULT_PORT);
        }

        @Test
        void worldWithObstacle() {
            // Given a world of size 2x2 with an obstacle at [1,1]
            assertTrue(serverClient.isConnected());

            // When launching 8 robots (all available positions except obstacle)
            for (int i = 0; i < 8; i++) {
                String initialRequest = "{" +
                        "\"robot\": \"RoboCop" + i + "\"," +
                        "\"command\": \"launch\"," +
                        "\"arguments\": [\"shooter\",\"5\",\"5\"]" +
                        "}";
                JsonNode initialResponse = serverClient.sendRequest(initialRequest);
                assertEquals("OK", initialResponse.get("result").asText());

                // Then each robot cannot be in position [1,1]
                JsonNode initialPosition = initialResponse.get("data").get("position");
                assertFalse(initialPosition.get(0).asInt() == 1 && initialPosition.get(1).asInt() == 1);
            }

        }

        @Test
        void worldWithObstacleIsFull() {
            // Given a world of size 2x2 with an obstacle at [1,1]
            assertTrue(serverClient.isConnected());

            //And the world has an obstacle at coordinate [1,1],
            //And I have successfully launched 8 robots into the world
            for (int i = 1; i < 9; i++) {
                String initialRequest = "{" +
                        "\"robot\": \"Terminator" + i + "\"," +
                        "\"command\": \"launch\"," +
                        "\"arguments\": [\"shooter\",\"5\",\"5\"]" +
                        "}";
                JsonNode initialResponse = serverClient.sendRequest(initialRequest);
                assertEquals("OK", initialResponse.get("result").asText());
            }

            //When I launch one more robot
            String request = "{" +
                    "\"robot\": \"T1000\"," +
                    "\"command\": \"launch\"," +
                    "\"arguments\": [\"shooter\",\"5\",\"5\"]" +
                    "}";
            JsonNode response = serverClient.sendRequest(request);

            //Then I should get an error response back with the message "No more space in this world."
            assertNotNull(response.get("result"));
            assertEquals("ERROR", response.get("result").asText());
            assertNotNull(response.get("data"));
            assertNotNull(response.get("data").get("message"));
            assertEquals("No more space in this world", response.get("data").get("message").asText());
        }
    }
}