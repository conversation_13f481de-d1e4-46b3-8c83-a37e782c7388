package za.co.wethinkcode.robots.AcceptanceTests;

import com.fasterxml.jackson.databind.JsonNode;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import za.co.wethinkcode.robots.client.RobotWorldClient;
import za.co.wethinkcode.robots.client.RobotWorldJsonClient;
import za.co.wethinkcode.robots.server.ConfigLoader;

import java.io.IOException;
import java.net.Socket;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;

/**
 * As a player
 * I want to get the state of a robot
 * So that I know its information at a given point in the game
 */
class  StateRobotTests {
    private final static int DEFAULT_PORT = 5000;
    private final static String DEFAULT_IP = "localhost";
    private final RobotWorldClient serverClient = new RobotWorldJsonClient();
    private final ConfigLoader configLoader = new ConfigLoader();
    private String url;
    private Process serverProcess;

    @BeforeEach
    void connectToServer() throws IOException {
        startServer("-s", "1"); // Start with 1x1 world size for this test
        waitForServerToStart(DEFAULT_IP, DEFAULT_PORT, 10000); // Wait up to 10 seconds
        serverClient.connect(DEFAULT_IP, DEFAULT_PORT);
    }

    @AfterEach
    void disconnectAndStopServer() {
        try {
            if (serverClient.isConnected()) {
                serverClient.disconnect();
            }
        } catch (RuntimeException e) {
            // Server may have already disconnected - this is acceptable
            System.out.println("Server already disconnected: " + e.getMessage());
        }

        // Kill server process
        if (serverProcess != null && serverProcess.isAlive()) {
            serverProcess.destroyForcibly();
            try {
                serverProcess.waitFor();
                Thread.sleep(500);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }
    }

    // Helper methods to reduce duplication
    private void startServer(String... additionalArgs) throws IOException {
        // Load server url from properties file
        url = configLoader.loadServerUrl("serverConfig.properties");

        ProcessBuilder pb = new ProcessBuilder();
        pb.command().add("java");
        pb.command().add("-jar");
        pb.command().add(url);

        // Add any additional arguments (like -s 1)
        for (String arg : additionalArgs) {
            pb.command().add(arg);
        }

        serverProcess = pb.start();
        System.out.println("Started reference server with args: " + String.join(" ", additionalArgs));
    }

    /**
     * Waits for the server to start and become available for connections.
     * Uses a retry mechanism with exponential backoff.
     *
     * @param host The host to connect to
     * @param port The port to connect to
     * @param timeoutMs Maximum time to wait in milliseconds
     * @throws RuntimeException if server doesn't start within the timeout
     */
    private void waitForServerToStart(String host, int port, long timeoutMs) {
        long startTime = System.currentTimeMillis();
        long waitTime = 100; // Start with 100ms wait

        while (System.currentTimeMillis() - startTime < timeoutMs) {
            try (Socket testSocket = new Socket(host, port)) {
                // If we can connect, the server is ready
                System.out.println("Server is ready for connections");
                return;
            } catch (IOException e) {
                // Server not ready yet, wait and retry
                try {
                    Thread.sleep(waitTime);
                    waitTime = Math.min(waitTime * 2, 1000); // Exponential backoff, max 1 second
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    throw new RuntimeException("Interrupted while waiting for server to start", ie);
                }
            }
        }

        throw new RuntimeException("Server failed to start within " + timeoutMs + "ms");
    }

    @Test
    void validStateShouldSucceed(){
        // Given that I am connected to a running Robot Worlds server
        // And the world is of size 1x1
        assertTrue(serverClient.isConnected());

        // And I have launched a robot successfully
        String launchRequest = "{" +
                "\"robot\": \"HAL\"," +
                "\"command\": \"launch\"," +
                "\"arguments\": [\"sniper\",\"0\",\"0\"]" +
                "}";

        JsonNode response = serverClient.sendRequest(launchRequest);
        assertNotNull(response.get("result"));
        assertEquals("OK", response.get("result").asText());

        // When I send a valid state request to the server
        String stateRequest = "{" +
                "\"robot\": \"HAL\"," +
                "\"command\": \"state\"," +
                "\"arguments\": [\"sniper\",\"0\",\"0\"]" +
                "}";

        JsonNode stateResponse = serverClient.sendRequest(stateRequest);

        // Then I should get a valid response from the server
        assertEquals("OK", stateResponse.get("result").asText());

        // And I should get the state of the robot
        assertNotNull(response.get("state"));
    }
    @Test
    void invalidStateShouldFail() {
        // Given that I am connected to a running Robot World Server
        assertTrue(serverClient.isConnected());

        // When I ask for the state of a non-existent robot
        String stateRequest = "{" +
                "\"robot\": \"NON_EXISTENT_ROBOT\"," +
                "\"command\": \"state\"," +
                "\"arguments\": [\"tank\", \"5\", \"5\"]" +
                "}";
        JsonNode response = serverClient.sendRequest(stateRequest);

        // Then the response should indicate an error
        assertEquals("ERROR", response.get("result").asText());
        assertNotNull(response.get("data").get("message"));
    }
}

