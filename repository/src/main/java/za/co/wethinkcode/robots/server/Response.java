package za.co.wethinkcode.robots.server;
import org.json.JSONException;
import org.json.JSONObject;

/**
 * Represents a standardized response object used for communication between the client and server.
 * Encapsulates a JSON structure with result, message, and optional data for consistency.
 */
public class Response {
    public final JSONObject object;

    public static Response responseFromJSONString(String string) {
        try {
            JSONObject jsonObject = new JSONObject(string);

            return new Response(jsonObject.getString("result"), jsonObject.getString("message"));
        } catch (JSONException e) {
            return new Response("Invalid JSON", "");
        }
    }

    public Response(String result, String message) {
        this.object = new JSONObject();
        JSONObject data = new JSONObject();
        this.object.put("result", result);
        data.put("message", message);
        this.object.put("data", data);
    }

    public Response(JSONObject jsonObject) {
        this.object = jsonObject;
    }

    public static Response ok(JSONObject data, String message, JSONObject state) {
        JSONObject response = new JSONObject();
        response.put("result", "OK");
        response.put("message", message);
        response.put("data", data);
        response.put("state", state);
        return new Response(response);
    }

    public String getMessage() {
        return object.getString("message");
    }

    public String toJSONString() {
        return this.object.toString();
    }

    public boolean isOKResponse() {
        return this.object.getString("result").equalsIgnoreCase("OK");
    }
}
