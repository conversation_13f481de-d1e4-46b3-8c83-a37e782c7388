package za.co.wethinkcode.robots.commands;

import za.co.wethinkcode.robots.Robot;
import za.co.wethinkcode.robots.client.Position;
import za.co.wethinkcode.robots.server.Response;
import za.co.wethinkcode.robots.server.Status;
import za.co.wethinkcode.robots.server.World;

public class MoveCommand extends Command {
    private final String direction;

    public MoveCommand(Robot robot, String direction, String[] arguments) {
        super(robot, arguments);
        this.direction = direction.toLowerCase();
    }

    @Override
    public String commandName() {
        return direction;
    }

    @Override
    public Response execute(World world) {
        // Parse steps
        int steps = parseSteps();
        if (steps == -1) {
            return new Response("ERROR", "Invalid number of steps.");
        }

        // Find robot in a world
        Robot worldRobot = world.findRobot(robot.getName());
        if (worldRobot == null) {
            return new Response("ERROR", "Robot not found.");
        }

        // Validate robot state
        if (worldRobot.status == Robot.RobotStatus.Dead) {
            return new Response("ERROR", worldRobot.getName() + " is DEAD and cannot move.");
        }

        // Calculate target position
        Position targetPos = calculateTargetPosition(worldRobot, steps);
        
        // Check bounds
        if (!isWithinWorldBounds(targetPos, world)) {
            Response response = new Response("OK", "At the " + worldRobot.orientation() + " edge");
            world.stateForRobot(worldRobot, response);
            return response;
        }

        // Check obstacles
        Status pathStatus = checkPath(worldRobot, steps, world);
        if (pathStatus != Status.OK) {
            return handleObstacle(worldRobot, pathStatus, world);
        }

        // Execute movement
        executeMovement(worldRobot, steps);
        
        Response response = new Response("OK", "Done");
        world.stateForRobot(worldRobot, response);
        return response;
    }

    private int parseSteps() {
        try {
            return Integer.parseInt(arguments[0]);
        } catch (NumberFormatException | ArrayIndexOutOfBoundsException e) {
            return -1;
        }
    }

    private Position calculateTargetPosition(Robot robot, int steps) {
        String moveDirection = direction.equals("forward") ? robot.orientation() : oppositeDirection(robot.orientation());
        return calculatePosition(robot.getPosition(), moveDirection, steps);
    }

    private boolean isWithinWorldBounds(Position pos, World world) {
        return pos.getX() >= -world.getHalfWidth() && 
               pos.getX() <= world.getHalfWidth() &&
               pos.getY() >= -world.getHalfHeight() && 
               pos.getY() <= world.getHalfHeight();
    }

    private Status checkPath(Robot robot, int steps, World world) {
        Position current = robot.getPosition();

        // Determine movement direction
        String moveDirection = direction.equals("forward") ? robot.orientation() : oppositeDirection(robot.orientation());

        // Check each step along the path
        for (int step = 1; step <= steps; step++) {
            Position nextPos = calculatePosition(current, moveDirection, step);
            Status status = world.isPositionValid(nextPos);
            if (status != Status.OK) {
                return status; 
            }
        }
        // Path is clear
        return Status.OK; 
    }

    // Helper: Get opposite direction
    private String oppositeDirection(String direction) {
        return switch (direction) {
            case "NORTH" -> "SOUTH";
            case "SOUTH" -> "NORTH";
            case "EAST" -> "WEST";
            case "WEST" -> "EAST";
            default -> direction;
        };
    }

    // Helper: Calculate position after moving
    private Position calculatePosition(Position start, String direction, int steps) {
        return switch (direction) {
            case "NORTH" -> new Position(start.getX(), start.getY() + steps);
            case "SOUTH" -> new Position(start.getX(), start.getY() - steps);
            case "EAST"  -> new Position(start.getX() + steps, start.getY());
            case "WEST"  -> new Position(start.getX() - steps, start.getY());
            default      -> start;
        };

    }

    private Response handleObstacle(Robot robot, Status status, World world) {
        Response response;
        if (status == Status.HitObstaclePIT) {
            robot.status = Robot.RobotStatus.Dead;
            response = new Response("ERROR", robot.getName() + " fell into a pit and died.");
        } else {
            response = new Response("ERROR", "At the " + robot.orientation() + " obstacle");
        }
        world.stateForRobot(robot, response);
        return response;
    }

    private void executeMovement(Robot robot, int steps) {
        if (direction.equals("forward")) {
            robot.moveForward(steps);
        } else if (direction.equals("back")) {
            robot.moveBackward(steps);
        }
    }
}