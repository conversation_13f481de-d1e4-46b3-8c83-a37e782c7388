# Configuration
PORT ?= 5000
LIBS_DIR := libs
SERVER_JAR := $(LIBS_DIR)/reference-server-0.2.3.jar
VERSION ?= $(shell mvn help:evaluate -Dexpression=project.version -q -DforceStdout)
NEXT_VERSION ?= $(shell echo $(VERSION) | awk -F. '{$$NF = $$NF + 1; print}' OFS=.)
OWN_SERVER_JAR := target/robot-world-$(VERSION)-jar-with-dependencies.jar
ACCEPTANCE_TESTS := "**/AcceptanceTests/**"

# Targets
.PHONY: all compile test package server client StartServer StopServer clean help \
        version bump-version tag-release release dev-build release-build \
        test-reference test-own remove-snapshot add-snapshot verify

# Default target - development build
all: dev-build

# Core Maven tasks
compile:
	mvn compile

verify:
	mvn verify

build:
	mvn test
package:
	mvn package -DskipTests

# Run applications from source
server:
	mvn exec:java -Dexec.mainClass="za.co.wethinkcode.robots.server.Server"

client:
	mvn exec:java -Dexec.mainClass="za.co.wethinkcode.robots.client.ClientApp"

# Server management
StartServer:
	@if [ ! -f "$(SERVER_JAR)" ]; then \
		echo "Error: Reference server JAR not found at $(SERVER_JAR)"; \
		exit 1; \
	fi
	@echo "Starting reference server on port $(PORT)..."
	java -jar $(SERVER_JAR) $(PORT) &
	@echo "Reference server started. PID: $$!"

StopServer:
	@PID=$$(lsof -t -i :${PORT} 2>/dev/null); \
	if [ -n "$$PID" ]; then \
		echo "Stopping server (PID $$PID) on port ${PORT}"; \
		kill $$PID; \
		sleep 1; \
		echo "Server stopped"; \
	else \
		echo "No server running on port ${PORT}"; \
	fi

StartOwnServer:
	@if [ ! -f "$(OWN_SERVER_JAR)" ]; then \
		echo "Error: Own server JAR not found at $(OWN_SERVER_JAR)"; \
		echo "Run 'make package' first to build the JAR"; \
		exit 1; \
	fi
	@echo "Starting own server on port $(PORT)..."
	java -jar $(OWN_SERVER_JAR) $(PORT) &
	@echo "Own server started. PID: $$!"

# Testing against different servers
test-reference:
	@echo "Testing against reference server..."
	$(MAKE) StartServer
	@sleep 3
	@echo "Running acceptance tests against reference server..."
	mvn test -Dtest=$(ACCEPTANCE_TESTS) || ($(MAKE) StopServer && exit 1)
	$(MAKE) StopServer
	@echo "Reference server tests completed successfully"

test-own: package
	@echo "Testing against own server..."
	$(MAKE) StartOwnServer
	@sleep 3
	@echo "Running acceptance tests against own server..."
	mvn test -Dtest=$(ACCEPTANCE_TESTS) || ($(MAKE) StopServer && exit 1)
	$(MAKE) StopServer
	@echo "Own server tests completed successfully"

# Test only our server
test-acceptance: test-own

# Run all acceptance tests against both server
test-all: test-reference test-own
	@echo "All acceptance tests completed successfully"

# Version management
remove-snapshot:
	@echo "Removing SNAPSHOT from version..."
	sed -i.bak 's/-SNAPSHOT//' pom.xml
	@echo "SNAPSHOT removed from version"

add-snapshot:
	@echo "Adding SNAPSHOT to version..."
	sed -i.bak 's/<version>\([0-9.]*\)<\/version>/<version>\1-SNAPSHOT<\/version>/' pom.xml
	@echo "SNAPSHOT added to version"

clean:
	mvn clean
	rm -rf target/

version:
	@echo "Current version: $(VERSION)"
	@echo "Next version: $(NEXT_VERSION)"
	@echo "Own server JAR: $(OWN_SERVER_JAR)"

bump-version:
	mvn versions:set -DnewVersion=$(NEXT_VERSION) -DgenerateBackupPoms=false
	@echo "Version bumped to $(NEXT_VERSION)"

tag-release:
	@CLEAN_VERSION=$$(echo $(VERSION) | sed 's/-SNAPSHOT//'); \
	git tag -a release-$$CLEAN_VERSION -m "Release version $$CLEAN_VERSION"; \
	echo "Tag release-$$CLEAN_VERSION created. Push with: git push origin release-$$CLEAN_VERSION"

# Build types
dev-build: clean compile test package
	@echo "Development build completed successfully"

release-build: remove-snapshot clean verify compile test-reference test-own package add-snapshot tag-release
	@echo "Release build completed successfully"

# Legacy release target (for backward compatibility)
release: release-build

help:
	@echo "Available targets:"
	@echo ""
	@echo "Build targets:"
	@echo "  all           - Development build (default)"
	@echo "  dev-build     - Development build (clean, compile, test, package)"
	@echo "  release-build - Full release build with all tests and tagging"
	@echo "  release       - Alias for release-build"
	@echo ""
	@echo "Core Maven tasks:"
	@echo "  compile       - Compile source code"
	@echo "  verify        - Verify dependencies"
	@echo "  test          - Run unit tests"
	@echo "  package       - Build JAR package"
	@echo ""
	@echo "Application runners:"
	@echo "  server        - Start server from source code"
	@echo "  client        - Start client from source code"
	@echo ""
	@echo "Server management:"
	@echo "  StartServer     - Start reference server from .libs/"
	@echo "  StartOwnServer  - Start own server from target/"
	@echo "  StopServer      - Stop any server on configured port"
	@echo ""
	@echo "Testing:"
	@echo "  test-reference  - Run acceptance tests against reference server"
	@echo "  test-own        - Run acceptance tests against own server"
	@echo ""
	@echo "Version management:"
	@echo "  version         - Show version information"
	@echo "  bump-version    - Increment version number"
	@echo "  remove-snapshot - Remove SNAPSHOT from version"
	@echo "  add-snapshot    - Add SNAPSHOT to version"
	@echo "  tag-release     - Create Git release tag"
	@echo ""
	@echo "Utilities:"
	@echo "  clean           - Remove build artifacts"
	@echo "  help            - Show this help message"
	@echo ""
	@echo "Configuration:"
	@echo "  PORT           = $(PORT)"
	@echo "  LIBS_DIR       = $(LIBS_DIR)"
	@echo "  SERVER_JAR     = $(SERVER_JAR)"
	@echo "  OWN_SERVER_JAR = $(OWN_SERVER_JAR)"
	@echo "  VERSION        = $(VERSION)"
	@echo "  NEXT_VERSION   = $(NEXT_VERSION)"