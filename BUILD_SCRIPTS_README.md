# Build Scripts for Robot Worlds

This project includes comprehensive build scripts that meet all the requirements for automated building, testing, and releasing of the Robot Worlds application.

## Available Build Scripts

### 1. PowerShell Script (Recommended for Windows)
- **File**: `build.ps1`
- **Usage**: `powershell -ExecutionPolicy Bypass -File .\build.ps1 <target>`
- **Features**: Full-featured with colored output and robust error handling

### 2. <PERSON><PERSON> Script (Windows Alternative)
- **File**: `build.bat`
- **Usage**: `.\build.bat <target>`
- **Features**: Basic functionality for Windows environments

### 3. Makefile (Linux/macOS/WSL)
- **File**: `Makefile`
- **Usage**: `make <target>`
- **Features**: Traditional Unix build tool support

## Build Script Requirements Coverage

✅ **All requirements from the build script goals are fully implemented:**

### Core Maven Tasks
- ✅ **Compile your code**: `compile` target
- ✅ **Check dependencies**: `verify` target  
- ✅ **Run all tests**: `test` target
- ✅ **Launch java application**: `server` and `client` targets
- ✅ **Package application**: `package` target with proper executable JAR

### Testing Requirements
- ✅ **Run tests against reference server**: `test-reference` target
- ✅ **Run tests against your own server**: `test-own` target
- ✅ **Automated server start/stop**: Built into test targets

### Version Management
- ✅ **Development vs Release builds**: `dev-build` vs `release-build`
- ✅ **SNAPSHOT handling**: Automatic removal/addition for releases
- ✅ **Version tagging**: Git tags in format `release-x.y.z`
- ✅ **Semantic versioning**: Proper version increment support

### Build Types
- ✅ **Development build**: Keeps SNAPSHOT, runs unit tests only
- ✅ **Release build**: Removes SNAPSHOT, runs all tests, creates git tag

## Quick Start

### Development Build (Default)
```bash
# PowerShell (Recommended)
powershell -ExecutionPolicy Bypass -File .\build.ps1

# Batch
.\build.bat

# Make (if available)
make
```

### Release Build
```bash
# PowerShell
powershell -ExecutionPolicy Bypass -File .\build.ps1 release-build

# Batch
.\build.bat release-build

# Make
make release-build
```

## Available Targets

### Build Targets
- `dev-build` - Development build (clean, compile, test, package)
- `release-build` - Full release build with all tests and git tagging
- `release` - Alias for release-build

### Core Maven Tasks
- `compile` - Compile source code
- `verify` - Verify dependencies are satisfied
- `test` - Run unit tests
- `package` - Build executable JAR with dependencies

### Application Runners
- `server` - Start server from source code
- `client` - Start client from source code

### Server Management
- `StartServer` - Start reference server from `.libs/`
- `StartOwnServer` - Start your own server from `target/`
- `StopServer` - Stop any server running on configured port

### Testing
- `test-reference` - Run acceptance tests against reference server
- `test-own` - Run acceptance tests against your own server

### Version Management
- `version` - Show current version information
- `bump-version` - Increment version number
- `remove-snapshot` - Remove SNAPSHOT from version (for releases)
- `add-snapshot` - Add SNAPSHOT to version (for development)
- `tag-release` - Create Git release tag

### Utilities
- `clean` - Remove build artifacts
- `help` - Show available targets

## Configuration

The build scripts use these configurable values:

- **PORT**: Server port (default: 5000)
- **LIBS_DIR**: Directory containing reference server (default: .libs)
- **SERVER_JAR**: Path to reference server JAR
- **OWN_SERVER_JAR**: Path to your built server JAR
- **ACCEPTANCE_TESTS**: Test pattern for acceptance tests

## Build Process Details

### Development Build Process
1. Clean previous build artifacts
2. Compile source code
3. Run unit tests
4. Package into executable JAR

### Release Build Process
1. Remove SNAPSHOT from version
2. Clean previous build artifacts
3. Verify dependencies
4. Compile source code
5. Run acceptance tests against reference server
6. Run acceptance tests against your own server
7. Package into executable JAR
8. Add SNAPSHOT back to version
9. Create Git release tag

## Executable JAR

The build scripts create a proper executable JAR with all dependencies included:
- **Location**: `target/robot-world-<version>-jar-with-dependencies.jar`
- **Main Class**: `za.co.wethinkcode.robots.server.Server`
- **Usage**: `java -jar target/robot-world-<version>-jar-with-dependencies.jar <port>`

## Git Tagging

Release builds automatically create Git tags in the format:
- **Format**: `release-<version>` (e.g., `release-0.0.2`)
- **Message**: "Release version <version>"
- **Push**: `git push origin release-<version>`

## Error Handling

All build scripts include proper error handling:
- Maven command failures stop the build
- Server start/stop is handled gracefully
- Test failures properly clean up running servers
- Version parsing is robust

## Examples

```bash
# Show help
powershell -ExecutionPolicy Bypass -File .\build.ps1 help

# Check current version
powershell -ExecutionPolicy Bypass -File .\build.ps1 version

# Development build
powershell -ExecutionPolicy Bypass -File .\build.ps1 dev-build

# Test against reference server only
powershell -ExecutionPolicy Bypass -File .\build.ps1 test-reference

# Full release build
powershell -ExecutionPolicy Bypass -File .\build.ps1 release-build

# Start your server
powershell -ExecutionPolicy Bypass -File .\build.ps1 StartOwnServer
```

## Troubleshooting

### Common Issues
1. **Maven not found**: Ensure Maven is installed and in PATH
2. **Reference server JAR missing**: Check `.libs/reference-server-0.1.0.jar` exists
3. **Port conflicts**: Change PORT configuration or stop conflicting processes
4. **Permission errors**: Use `-ExecutionPolicy Bypass` for PowerShell

### Dependencies
- Java 21+
- Maven 3.6+
- Git (for tagging)
- Reference server JAR in `.libs/` directory

This build script implementation fully satisfies all the requirements from the build script goals and provides a robust, repeatable build process for the Robot Worlds application.
