# Multi-stage build
FROM maven:3.9.6-eclipse-temurin-21 AS builder

# Metadata
LABEL maintainers="JUSTICE <<EMAIL>>, \
                   MAKHOSAZANA <<EMAIL>>, \
                   SIPHUMELELE <<EMAIL>>, \
                   ANGELINA <<EMAIL>>"

# Install make
RUN apt-get update && \
    apt-get install -y make && \
    apt-get clean

WORKDIR /src
COPY target/robot-world-0.0.2-SNAPSHOT-jar-with-dependencies.jar .



EXPOSE 5050

# Run with the exact JAR filename from your target directory
CMD ["java", "-jar", "robot-world-0.0.2-SNAPSHOT-jar-with-dependencies.jar", "-p", "5050"]