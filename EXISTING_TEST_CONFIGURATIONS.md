# Robot World Test Configurations for Existing Tests

This document explains how to run the existing `LaunchRobotTest.java` and `LaunchRobot2x2Test.java` with different world configurations.

## Test Organization

The existing test files have been organized to support different world configurations:

### 1. **LaunchRobotTest.java** - 1x1 World Tests
- **1x1 Empty World**: Basic launch tests (1 robot capacity)
- **1x1 World with Obstacle**: No launch possible (0 robot capacity)

### 2. **LaunchRobot2x2Test.java** - 2x2 World Tests  
- **2x2 Empty World**: Multiple robot launches (9 robot capacity)
- **2x2 World with Obstacle**: Obstacle avoidance (8 robot capacity)

## How to Run Tests

### Step 1: Build the Project
```bash
mvn clean package -DskipTests
```

### Step 2: Start Server with Appropriate Configuration

#### For LaunchRobotTest.java:

**1x1 Empty World Tests (Orders 1-3):**
```bash
java -jar target/robot-world-0.0.2-SNAPSHOT-jar-with-dependencies.jar -s 1
```
Then run:
```bash
mvn test -Dtest=LaunchRobotTest#launch1x1EmptyWorldSuccess
mvn test -Dtest=LaunchRobotTest#launch1x1EmptyWorldFull
mvn test -Dtest=LaunchRobotTest#launch1x1EmptyWorldDuplicateName
```

**1x1 World with Obstacle Tests (Order 4):**
```bash
java -jar target/robot-world-0.0.2-SNAPSHOT-jar-with-dependencies.jar -s 1 -o 0,0
```
Then run:
```bash
mvn test -Dtest=LaunchRobotTest#launch1x1WorldWithObstacle
```

#### For LaunchRobot2x2Test.java:

**1x1 Empty World Tests (Orders 1-2):**
```bash
java -jar target/robot-world-0.0.2-SNAPSHOT-jar-with-dependencies.jar -s 1
```
Then run:
```bash
mvn test -Dtest=LaunchRobot2x2Test#launch1x1EmptyWorldSuccess
mvn test -Dtest=LaunchRobot2x2Test#launch1x1EmptyWorldFull
```

**1x1 World with Obstacle Tests (Order 3):**
```bash
java -jar target/robot-world-0.0.2-SNAPSHOT-jar-with-dependencies.jar -s 1 -o 0,0
```
Then run:
```bash
mvn test -Dtest=LaunchRobot2x2Test#launch1x1WorldWithObstacle
```

**2x2 Empty World Tests (Orders 4-5):**
```bash
java -jar target/robot-world-0.0.2-SNAPSHOT-jar-with-dependencies.jar -s 2
```
Then run:
```bash
mvn test -Dtest=LaunchRobot2x2Test#launch2x2EmptyWorldMultipleRobots
mvn test -Dtest=LaunchRobot2x2Test#launch2x2EmptyWorldFull
```

**2x2 World with Obstacle Tests (Orders 6-7):**
```bash
java -jar target/robot-world-0.0.2-SNAPSHOT-jar-with-dependencies.jar -s 2 -o 1,1
```
Then run:
```bash
mvn test -Dtest=LaunchRobot2x2Test#launch2x2WorldWithObstacleRobotsAvoidObstacle
mvn test -Dtest=LaunchRobot2x2Test#launch2x2WorldWithObstacleFull
```

### Step 3: Run All Tests for a Configuration

#### Run All Tests in a File:
```bash
# All LaunchRobotTest tests (requires different server configs)
mvn test -Dtest=LaunchRobotTest

# All LaunchRobot2x2Test tests (requires different server configs)
mvn test -Dtest=LaunchRobot2x2Test
```

## Test Details

### LaunchRobotTest.java Test Methods:

1. **`launch1x1EmptyWorldSuccess`** (Order 1)
   - **Config**: 1x1 empty world
   - **Expected**: Robot launches at (0,0)

2. **`launch1x1EmptyWorldFull`** (Order 2)
   - **Config**: 1x1 empty world
   - **Expected**: Second robot fails (world full)

3. **`launch1x1EmptyWorldDuplicateName`** (Order 3)
   - **Config**: 1x1 empty world
   - **Expected**: Duplicate robot name fails

4. **`launch1x1WorldWithObstacle`** (Order 4)
   - **Config**: 1x1 world with obstacle at (0,0)
   - **Expected**: No robots can launch (0 capacity)

5. **`launchInvalidCommand`** (Order 5)
   - **Config**: Any world
   - **Expected**: Invalid command fails

### LaunchRobot2x2Test.java Test Methods:

1. **`launch1x1EmptyWorldSuccess`** (Order 1)
   - **Config**: 1x1 empty world
   - **Expected**: Robot launches at (0,0)

2. **`launch1x1EmptyWorldFull`** (Order 2)
   - **Config**: 1x1 empty world
   - **Expected**: Second robot fails (world full)

3. **`launch1x1WorldWithObstacle`** (Order 3)
   - **Config**: 1x1 world with obstacle at (0,0)
   - **Expected**: No robots can launch (0 capacity)

4. **`launch2x2EmptyWorldMultipleRobots`** (Order 4)
   - **Config**: 2x2 empty world
   - **Expected**: Multiple robots launch successfully

5. **`launch2x2EmptyWorldFull`** (Order 5)
   - **Config**: 2x2 empty world
   - **Expected**: 10th robot fails (world full after 9)

6. **`launch2x2WorldWithObstacleRobotsAvoidObstacle`** (Order 6)
   - **Config**: 2x2 world with obstacle at (1,1)
   - **Expected**: 8 robots launch, none at (1,1)

7. **`launch2x2WorldWithObstacleFull`** (Order 7)
   - **Config**: 2x2 world with obstacle at (1,1)
   - **Expected**: 9th robot fails (world full after 8)

## Expected Test Results

### 1x1 Empty World:
- **Capacity**: 1 robot
- **First robot**: Launches at (0,0)
- **Second robot**: Fails with "world full"

### 1x1 World with Obstacle at (0,0):
- **Capacity**: 0 robots
- **Any robot**: Fails with "no more space"

### 2x2 Empty World:
- **Capacity**: 9 robots
- **Positions**: (-1,-1), (-1,0), (-1,1), (0,-1), (0,0), (0,1), (1,-1), (1,0), (1,1)
- **10th robot**: Fails with "world full"

### 2x2 World with Obstacle at (1,1):
- **Capacity**: 8 robots (9 positions - 1 obstacle)
- **Robots avoid**: Position (1,1)
- **9th robot**: Fails with "world full"

## Quick Test Commands

### Test 1x1 Empty World:
```bash
# Start server
java -jar target/robot-world-0.0.2-SNAPSHOT-jar-with-dependencies.jar -s 1

# Run tests
mvn test -Dtest=LaunchRobotTest#launch1x1EmptyWorldSuccess
mvn test -Dtest=LaunchRobotTest#launch1x1EmptyWorldFull
```

### Test 1x1 World with Obstacle:
```bash
# Start server
java -jar target/robot-world-0.0.2-SNAPSHOT-jar-with-dependencies.jar -s 1 -o 0,0

# Run test
mvn test -Dtest=LaunchRobotTest#launch1x1WorldWithObstacle
```

### Test 2x2 Empty World:
```bash
# Start server
java -jar target/robot-world-0.0.2-SNAPSHOT-jar-with-dependencies.jar -s 2

# Run tests
mvn test -Dtest=LaunchRobot2x2Test#launch2x2EmptyWorldMultipleRobots
mvn test -Dtest=LaunchRobot2x2Test#launch2x2EmptyWorldFull
```

### Test 2x2 World with Obstacle:
```bash
# Start server
java -jar target/robot-world-0.0.2-SNAPSHOT-jar-with-dependencies.jar -s 2 -o 1,1

# Run tests
mvn test -Dtest=LaunchRobot2x2Test#launch2x2WorldWithObstacleRobotsAvoidObstacle
mvn test -Dtest=LaunchRobot2x2Test#launch2x2WorldWithObstacleFull
```

## Troubleshooting

### Port Already in Use:
```bash
# Find and kill process using port 5000
netstat -ano | findstr :5000
taskkill /PID <PID> /F
```

### Wrong World Configuration:
Make sure you start the server with the correct configuration for the specific test you're running. Each test expects a specific world setup as documented above.
