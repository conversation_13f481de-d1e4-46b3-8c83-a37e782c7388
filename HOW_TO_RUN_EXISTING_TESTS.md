# How to Run Existing Tests with Different World Configurations

This guide explains how to run your existing `LaunchRobotTest.java` and `LaunchRobot2x2Test.java` files with different world configurations **without changing the test structure**.

## Your Existing Test Files

### 1. **LaunchRobotTest.java** - Contains 4 test methods:
- `ValidLaunchShouldSucceed()` - Tests basic robot launch
- `InvalidLaunchShouldFail()` - Tests invalid command handling  
- `LaunchShouldFailWhenWorldIsFull()` - Tests world capacity limits
- `LaunchShouldFailWhenRobotNameExists()` - Tests duplicate name handling

### 2. **LaunchRobot2x2Test.java** - Contains 4 test methods:
- `canLaunchAnotherRobotInLargerWorld()` - Tests multiple robot launches
- `worldWithoutObstaclesIsFull()` - Tests 2x2 world capacity (9 robots)
- `worldWithObstacle()` - Tests obstacle avoidance
- `worldWithObstacleIsFull()` - Tests 2x2 world with obstacle capacity (8 robots)

## How to Test Different World Configurations

### **1x1 Empty World**

**Start Server:**
```bash
java -jar target/robot-world-0.0.2-SNAPSHOT-jar-with-dependencies.jar -s 1
```

**Run Tests:**
```bash
# Test basic functionality in 1x1 world
mvn test -Dtest=LaunchRobotTest#ValidLaunchShouldSucceed

# Test world capacity (should fail after 1 robot)
mvn test -Dtest=LaunchRobotTest#LaunchShouldFailWhenWorldIsFull

# Test duplicate names
mvn test -Dtest=LaunchRobotTest#LaunchShouldFailWhenRobotNameExists

# Test invalid commands
mvn test -Dtest=LaunchRobotTest#InvalidLaunchShouldFail
```

**Expected Results:**
- First robot launches at (0,0)
- Second robot fails with "world full" error
- Duplicate names fail appropriately

---

### **1x1 World with Obstacle**

**Start Server:**
```bash
java -jar target/robot-world-0.0.2-SNAPSHOT-jar-with-dependencies.jar -s 1 -o 0,0
```

**Run Tests:**
```bash
# This should fail because obstacle blocks the only position
mvn test -Dtest=LaunchRobotTest#ValidLaunchShouldSucceed
```

**Expected Results:**
- All robot launches should fail with "no more space" because the obstacle blocks position (0,0)

---

### **2x2 Empty World**

**Start Server:**
```bash
java -jar target/robot-world-0.0.2-SNAPSHOT-jar-with-dependencies.jar -s 2
```

**Run Tests:**
```bash
# Test multiple robots in larger world
mvn test -Dtest=LaunchRobot2x2Test#canLaunchAnotherRobotInLargerWorld

# Test world capacity (should fail after 9 robots)
mvn test -Dtest=LaunchRobot2x2Test#worldWithoutObstaclesIsFull
```

**Expected Results:**
- Multiple robots can launch successfully
- World becomes full after 9 robots
- 10th robot fails with "world full" error

---

### **2x2 World with Obstacle**

**Start Server:**
```bash
java -jar target/robot-world-0.0.2-SNAPSHOT-jar-with-dependencies.jar -s 2 -o 1,1
```

**Run Tests:**
```bash
# Test obstacle avoidance
mvn test -Dtest=LaunchRobot2x2Test#worldWithObstacle

# Test world capacity with obstacle (should fail after 8 robots)
mvn test -Dtest=LaunchRobot2x2Test#worldWithObstacleIsFull
```

**Expected Results:**
- Robots launch successfully but avoid position (1,1)
- World becomes full after 8 robots (9 positions - 1 obstacle = 8 capacity)
- 9th robot fails with "world full" error

---

## Quick Test Commands

### Build Project:
```bash
mvn clean package -DskipTests
```

### Test All Scenarios:

**1. Test 1x1 Empty World:**
```bash
# Terminal 1: Start server
java -jar target/robot-world-0.0.2-SNAPSHOT-jar-with-dependencies.jar -s 1

# Terminal 2: Run tests
mvn test -Dtest=LaunchRobotTest
```

**2. Test 1x1 World with Obstacle:**
```bash
# Terminal 1: Start server
java -jar target/robot-world-0.0.2-SNAPSHOT-jar-with-dependencies.jar -s 1 -o 0,0

# Terminal 2: Run test (should fail)
mvn test -Dtest=LaunchRobotTest#ValidLaunchShouldSucceed
```

**3. Test 2x2 Empty World:**
```bash
# Terminal 1: Start server
java -jar target/robot-world-0.0.2-SNAPSHOT-jar-with-dependencies.jar -s 2

# Terminal 2: Run tests
mvn test -Dtest=LaunchRobot2x2Test
```

**4. Test 2x2 World with Obstacle:**
```bash
# Terminal 1: Start server
java -jar target/robot-world-0.0.2-SNAPSHOT-jar-with-dependencies.jar -s 2 -o 1,1

# Terminal 2: Run tests
mvn test -Dtest=LaunchRobot2x2Test#worldWithObstacle
mvn test -Dtest=LaunchRobot2x2Test#worldWithObstacleIsFull
```

## Understanding Test Behavior

### LaunchRobotTest.java Behavior:

| World Config | ValidLaunchShouldSucceed | LaunchShouldFailWhenWorldIsFull | Expected Behavior |
|--------------|--------------------------|--------------------------------|-------------------|
| 1x1 Empty | ✅ PASS | ✅ PASS | 1st robot at (0,0), 2nd fails |
| 1x1 with Obstacle | ❌ FAIL | ❌ FAIL | No robots can launch |
| 2x2 Empty | ✅ PASS | ❌ FAIL* | 1st robot launches, but 2nd also succeeds |

*Note: In 2x2 world, the test expects world to be full after 1 robot, but 2x2 has 9 positions.

### LaunchRobot2x2Test.java Behavior:

| World Config | canLaunchAnotherRobotInLargerWorld | worldWithoutObstaclesIsFull | worldWithObstacle | worldWithObstacleIsFull |
|--------------|-----------------------------------|----------------------------|-------------------|------------------------|
| 2x2 Empty | ✅ PASS | ✅ PASS | ❌ FAIL* | ❌ FAIL* |
| 2x2 with Obstacle | ✅ PASS | ❌ FAIL* | ✅ PASS | ✅ PASS |

*Note: Tests expect specific obstacle behavior that may not match the world configuration.

## Key Points

1. **No Code Changes**: Your test files remain exactly as they are
2. **Server Configuration**: Different world setups are achieved by starting the server with different arguments
3. **Test Expectations**: Some tests may fail in certain configurations because they were designed for specific world setups
4. **Flexible Testing**: You can test any combination of world size and obstacles with your existing tests

## Troubleshooting

### Port Issues:
```bash
# Kill existing server
netstat -ano | findstr :5000
taskkill /PID <PID> /F
```

### Test Failures:
- Some test failures are expected when running tests in different world configurations than they were designed for
- This helps you understand how your server behaves in different scenarios
- Use the failures to identify areas where your server implementation needs improvement
