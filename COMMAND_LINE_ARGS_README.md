# Command Line Arguments Implementation - README

## 🔍 Overview
This document explains the implementation of command line arguments for the Robot Worlds server, allowing users to configure port, world size, and obstacles directly from the command line without modifying configuration files.

## 🚀 Features Implemented

### Command Line Arguments Support
The server now accepts the following command line arguments:

```bash
# Default behavior - port 5000, world from config file, no additional obstacles
java -jar target/robot-world-0.0.2-SNAPSHOT-jar-with-dependencies.jar

# Custom port
java -jar target/robot-world-0.0.2-SNAPSHOT-jar-with-dependencies.jar -p 8080

# Custom world size (creates square world)
java -jar target/robot-world-0.0.2-SNAPSHOT-jar-with-dependencies.jar -s 100

# Add obstacle at specific position (creates 1x1 obstacle)
java -jar target/robot-world-0.0.2-SNAPSHOT-jar-with-dependencies.jar -o 10,5

# All arguments combined
java -jar target/robot-world-0.0.2-SNAPSHOT-jar-with-dependencies.jar -p 8080 -s 100 -o 10,5
```

### Argument Details
- **`-p <port>`**: Set server port (default: 5000)
- **`-s <size>`**: Set world dimensions to size×size (overrides config file)
- **`-o <x,y>`**: Add obstacle at position [x,y] (creates 1×1 obstacle)

## 🛠 Implementation Details

### 1. ServerConfig Class
**File**: `src/main/java/za/co/wethinkcode/robots/server/ServerConfig.java`

**Purpose**: Parse and store command line arguments

**Key Features**:
```java
public class ServerConfig {
    private int port = 5000;           // Default port
    private int worldSize = 1;         // Default world size
    private String obstaclePosition;   // Optional obstacle position
    
    public static ServerConfig parseArgs(String[] args) {
        // Parses -p, -s, -o arguments
        // Handles invalid arguments gracefully
        // Provides help text for invalid usage
    }
}
```

**Argument Parsing Logic**:
- Uses simple loop to process argument pairs
- Validates numeric inputs (port, world size)
- Validates coordinate format for obstacles (x,y)
- Provides helpful error messages for invalid inputs

### 2. Enhanced ConfigLoader
**File**: `src/main/java/za/co/wethinkcode/robots/server/ConfigLoader.java`

**Enhancement**: Added method to accept command line overrides

```java
public void applyConfigToWorld(World world, String resourcePath, Integer overrideWidth, Integer overrideHeight) {
    // Use override values if provided, otherwise use config file values
    int width = overrideWidth != null ? overrideWidth : Integer.parseInt(properties.getProperty("world.width"));
    int height = overrideHeight != null ? overrideHeight : Integer.parseInt(properties.getProperty("world.height"));
    
    world.setDimensions(width, height);
    System.out.println("World successfully loaded with dimensions: " + width + " x " + height);
}
```

**Key Benefits**:
- Command line arguments take precedence over config file
- Maintains backward compatibility with existing config loading
- Proper error handling for missing or invalid config files

### 3. Server Main Method Refactoring
**File**: `src/main/java/za/co/wethinkcode/robots/server/Server.java`

**Changes Made**:
```java
public static void main(String[] args) {
    ServerConfig config = ServerConfig.parseArgs(args);
    int portNumber = config.getPort();
    
    World world = World.getInstance();
    
    // Apply configuration with command line overrides
    ConfigLoader configLoader = new ConfigLoader();
    Integer worldSize = config.getWorldSize() > 1 ? config.getWorldSize() : null;
    configLoader.applyConfigToWorld(world, "config.properties", worldSize, worldSize);
    
    // Add obstacle if specified
    if (config.getObstaclePosition() != null) {
        addObstacleFromPosition(world, config.getObstaclePosition());
    }
    
    // Start server with configured port
    startServer(portNumber, world);
}
```

### 4. World Class Modifications
**File**: `src/main/java/za/co/wethinkcode/robots/server/World.java`

**Changes**:
- Removed auto-configuration from constructor
- Added getter methods for dimensions
- Enhanced obstacle addition methods

```java
public World() {
    this.commandHandler = new CommandHandler(this);
    setDefaultDimensions();
    setDefaultWorldProperties();
    displayWorld();
}
```

### 5. Obstacle Addition Helper
**File**: `src/main/java/za/co/wethinkcode/robots/server/Server.java`

**Added Method**:
```java
private static void addObstacleFromPosition(World world, String position) {
    String[] coords = position.split(",");
    int x = Integer.parseInt(coords[0]);
    int y = Integer.parseInt(coords[1]);
    
    Obstacle obstacle = new Obstacle(ObstacleType.MOUNTAIN, x, y, 1, 1);
    if (world.addObstacle(obstacle)) {
        System.out.println("Added obstacle at position [" + x + "," + y + "]");
    } else {
        System.err.println("Failed to add obstacle at position [" + x + "," + y + "]");
    }
}
```

## 🎯 Key Concepts & Lessons Learned

### 1. **Command Line Argument Parsing**
**Concept**: Processing command line arguments in Java applications
**Implementation**: Manual parsing using loops and string operations
**Key Points**:
- Always validate input arguments
- Provide helpful error messages
- Use default values for optional parameters
- Handle edge cases (missing values, invalid formats)

### 2. **Configuration Override Pattern**
**Concept**: Allow runtime parameters to override configuration file settings
**Implementation**: 
```java
int value = commandLineValue != null ? commandLineValue : configFileValue;
```
**Benefits**:
- Flexibility without modifying config files
- Maintains default behavior when no overrides provided
- Easy testing with different configurations

### 3. **Singleton Pattern with Configuration**
**Challenge**: How to configure a singleton after creation
**Solution**: Separate initialization from configuration
**Pattern**:
```java
// Create instance with defaults
World world = World.getInstance();

// Configure after creation
configLoader.applyConfigToWorld(world, configFile, overrides);
```

### 4. **Separation of Concerns**
**Principle**: Each class should have a single responsibility
**Application**:
- `ServerConfig`: Parse and store command line arguments
- `ConfigLoader`: Load and apply configuration settings
- `Server`: Orchestrate application startup
- `World`: Manage game world state

### 5. **Graceful Error Handling**
**Concept**: Handle invalid inputs without crashing
**Implementation**:
```java
try {
    int port = Integer.parseInt(args[i + 1]);
    config.setPort(port);
} catch (NumberFormatException e) {
    System.err.println("Invalid port number: " + args[i + 1]);
    System.exit(1);
}
```

### 6. **Backward Compatibility**
**Principle**: New features shouldn't break existing functionality
**Implementation**:
- Kept original `applyConfigToWorld(world, resourcePath)` method
- Added overloaded version with additional parameters
- Default behavior remains unchanged when no arguments provided

### 7. **Configuration Precedence**
**Concept**: Establish clear hierarchy for configuration sources
**Hierarchy** (highest to lowest priority):
1. Command line arguments
2. Configuration file values
3. Hard-coded defaults

### 8. **Input Validation Patterns**
**Concept**: Validate all external inputs
**Patterns Applied**:
- Numeric validation for ports and sizes
- Format validation for coordinates (x,y)
- Range validation for reasonable values
- Null checking for optional parameters

### 9. **Factory Method Pattern**
**Concept**: Use static methods to create configured objects
**Implementation**:
```java
public static ServerConfig parseArgs(String[] args) {
    ServerConfig config = new ServerConfig();
    // Parse and configure
    return config;
}
```

### 10. **Dependency Injection Principles**
**Concept**: Pass dependencies rather than creating them internally
**Application**: Pass configured `World` instance to server rather than having server create it

## 🔧 Technical Implementation Patterns

### Pattern 1: Argument Parsing Loop
```java
for (int i = 0; i < args.length; i++) {
    switch (args[i]) {
        case "-p":
            // Parse next argument as port
            break;
        case "-s":
            // Parse next argument as size
            break;
    }
}
```

### Pattern 2: Override with Null Checking
```java
int finalValue = override != null ? override : defaultValue;
```

### Pattern 3: Builder-like Configuration
```java
ServerConfig config = ServerConfig.parseArgs(args);
World world = World.getInstance();
configLoader.applyConfigToWorld(world, "config.properties", 
    config.getWorldSize(), config.getWorldSize());
```

### Pattern 4: Validation with Early Return
```java
if (invalidInput) {
    System.err.println("Error message");
    System.exit(1);
}
// Continue with valid input
```

## 🚨 Common Issues & Solutions

### Issue 1: Configuration Loading Order
**Problem**: Config file overrides command line arguments
**Solution**: Load config with override parameters
**Fix**: Pass command line values to config loader

### Issue 2: Singleton Configuration
**Problem**: Can't configure singleton after creation
**Solution**: Separate creation from configuration
**Pattern**: Create → Configure → Use

### Issue 3: Display Rendering
**Problem**: Unicode emojis show as `??` in some terminals
**Cause**: Git Bash on Windows doesn't render Unicode emojis properly
**Solutions**:
- Use Windows Terminal or PowerShell
- Replace emojis with ASCII characters
- Use VS Code integrated terminal

### Issue 4: Argument Validation
**Problem**: Invalid arguments crash the application
**Solution**: Comprehensive input validation
**Pattern**: Validate → Error message → Exit gracefully

## 🎉 Results & Benefits

### Functionality Achieved
✅ **Port Configuration**: `-p 8080` sets custom port
✅ **World Size Override**: `-s 100` creates 100×100 world
✅ **Obstacle Addition**: `-o 10,5` adds obstacle at [10,5]
✅ **Combined Arguments**: All arguments work together
✅ **Backward Compatibility**: No arguments still works with defaults

### User Experience Improvements
- **Flexibility**: Configure server without editing files
- **Testing**: Easy to test different configurations
- **Deployment**: Single command with all settings
- **Documentation**: Clear help messages for invalid usage

### Code Quality Improvements
- **Separation of Concerns**: Clear class responsibilities
- **Error Handling**: Graceful handling of invalid inputs
- **Maintainability**: Easy to add new command line options
- **Testability**: Each component can be tested independently

## 🚀 Future Enhancements

### Potential Additions
1. **Multiple Obstacles**: `-o 10,5 -o 20,15` for multiple obstacles
2. **Obstacle Types**: `-o 10,5:LAKE` to specify obstacle type
3. **Configuration File Override**: `-c custom.properties`
4. **Verbose Mode**: `-v` for detailed startup logging
5. **Help Command**: `-h` or `--help` for usage information

### Advanced Features
1. **Environment Variable Support**: `ROBOT_WORLD_PORT=8080`
2. **JSON Configuration**: Support for JSON config files
3. **Profile Support**: `-profile production` for different configurations
4. **Validation Rules**: More sophisticated input validation

## 📊 Testing Strategy

### Manual Testing Performed
```bash
# Test default behavior
java -jar target/robot-world-0.0.2-SNAPSHOT-jar-with-dependencies.jar

# Test individual arguments
java -jar target/robot-world-0.0.2-SNAPSHOT-jar-with-dependencies.jar -p 8080
java -jar target/robot-world-0.0.2-SNAPSHOT-jar-with-dependencies.jar -s 50
java -jar target/robot-world-0.0.2-SNAPSHOT-jar-with-dependencies.jar -o 5,5

# Test combined arguments
java -jar target/robot-world-0.0.2-SNAPSHOT-jar-with-dependencies.jar -p 8080 -s 100 -o 10,5

# Test error cases
java -jar target/robot-world-0.0.2-SNAPSHOT-jar-with-dependencies.jar -p invalid
java -jar target/robot-world-0.0.2-SNAPSHOT-jar-with-dependencies.jar -s -5
java -jar target/robot-world-0.0.2-SNAPSHOT-jar-with-dependencies.jar -o invalid
```

### Verification Points
- ✅ Server starts on specified port
- ✅ World dimensions match specified size
- ✅ Obstacles appear at specified positions
- ✅ Invalid arguments show helpful error messages
- ✅ Default behavior unchanged when no arguments provided

This implementation provides a robust, user-friendly command line interface for configuring the Robot Worlds server while maintaining clean code architecture and comprehensive error handling.