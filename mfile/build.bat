@echo off
REM Build script for Robot Worlds - Windows Batch version
REM Equivalent to Makefile functionality

REM Configuration
set PORT=5000
set LIBS_DIR=.libs
set SERVER_JAR=%LIBS_DIR%\reference-server-0.1.0.jar
set ACCEPTANCE_TESTS=**/AcceptanceTests/**

REM Get version from pom.xml using PowerShell
setlocal enabledelayedexpansion
for /f "usebackq delims=" %%i in (`powershell -Command "$content = Get-Content 'pom.xml' -Raw; $match = [regex]::Match($content, '<version>([^<]+)</version>'); if ($match.Success) { $match.Groups[1].Value } else { '0.0.1-SNAPSHOT' }"`) do set VERSION=%%i

REM Get clean version (without SNAPSHOT)
set CLEAN_VERSION=!VERSION:-SNAPSHOT=!

REM Calculate next version (simplified)
set NEXT_VERSION=0.0.3

REM Set own server JAR path
set OWN_SERVER_JAR=target\robot-world-!CLEAN_VERSION!-jar-with-dependencies.jar

if "%1"=="" goto dev-build
if "%1"=="help" goto help
if "%1"=="all" goto dev-build
if "%1"=="dev-build" goto dev-build
if "%1"=="release-build" goto release-build
if "%1"=="compile" goto compile
if "%1"=="verify" goto verify
if "%1"=="test" goto test
if "%1"=="package" goto package
if "%1"=="server" goto server
if "%1"=="client" goto client
if "%1"=="StartServer" goto StartServer
if "%1"=="StartOwnServer" goto StartOwnServer
if "%1"=="StopServer" goto StopServer
if "%1"=="test-reference" goto test-reference
if "%1"=="test-own" goto test-own
if "%1"=="clean" goto clean
if "%1"=="version" goto version
if "%1"=="bump-version" goto bump-version
if "%1"=="remove-snapshot" goto remove-snapshot
if "%1"=="add-snapshot" goto add-snapshot
if "%1"=="tag-release" goto tag-release
if "%1"=="release" goto release-build

echo Unknown target: %1
echo Run "build.bat help" for available targets
exit /b 1

:dev-build
echo === Development Build ===
call :clean
call :compile
call :test
call :package
echo Development build completed successfully
goto :eof

:release-build
echo === Release Build ===
call :remove-snapshot
call :clean
call :verify
call :compile
call :test-reference
call :test-own
call :package
call :add-snapshot
call :tag-release
echo Release build completed successfully
goto :eof

:compile
echo Compiling source code...
mvn compile
if errorlevel 1 exit /b 1
goto :eof

:verify
echo Verifying dependencies...
mvn verify
if errorlevel 1 exit /b 1
goto :eof

:test
echo Running unit tests...
mvn test
if errorlevel 1 exit /b 1
goto :eof

:package
echo Building JAR package...
mvn package -DskipTests
if errorlevel 1 exit /b 1
goto :eof

:server
echo Starting server from source code...
mvn exec:java -Dexec.mainClass="za.co.wethinkcode.robots.server.Server"
goto :eof

:client
echo Starting client from source code...
mvn exec:java -Dexec.mainClass="za.co.wethinkcode.robots.client.ClientApp"
goto :eof

:StartServer
echo Starting reference server on port %PORT%...
if not exist "%SERVER_JAR%" (
    echo Error: Reference server JAR not found at %SERVER_JAR%
    exit /b 1
)
start /b java -jar "%SERVER_JAR%" %PORT%
echo Reference server started
goto :eof

:StartOwnServer
echo Starting own server on port %PORT%...
if not exist "%OWN_SERVER_JAR%" (
    echo Error: Own server JAR not found at %OWN_SERVER_JAR%
    echo Run 'build.bat package' first to build the JAR
    exit /b 1
)
start /b java -jar "%OWN_SERVER_JAR%" %PORT%
echo Own server started
goto :eof

:StopServer
echo Stopping server on port %PORT%...
for /f "tokens=5" %%a in ('netstat -ano ^| findstr :%PORT%') do (
    taskkill /PID %%a /F >nul 2>&1
)
echo Server stopped
goto :eof

:test-reference
echo Testing against reference server...
call :StartServer
timeout /t 3 /nobreak >nul
echo Running acceptance tests against reference server...
mvn test -Dtest=%ACCEPTANCE_TESTS%
set TEST_RESULT=%errorlevel%
call :StopServer
if %TEST_RESULT% neq 0 exit /b 1
echo Reference server tests completed successfully
goto :eof

:test-own
echo Testing against own server...
call :package
call :StartOwnServer
timeout /t 3 /nobreak >nul
echo Running acceptance tests against own server...
mvn test -Dtest=%ACCEPTANCE_TESTS%
set TEST_RESULT=%errorlevel%
call :StopServer
if %TEST_RESULT% neq 0 exit /b 1
echo Own server tests completed successfully
goto :eof

:clean
echo Cleaning build artifacts...
mvn clean
if exist target rmdir /s /q target
goto :eof

:version
echo Current version: %VERSION%
echo Next version: %NEXT_VERSION%
echo Own server JAR: %OWN_SERVER_JAR%
goto :eof

:bump-version
echo Bumping version to %NEXT_VERSION%...
mvn versions:set -DnewVersion=%NEXT_VERSION% -DgenerateBackupPoms=false
echo Version bumped to %NEXT_VERSION%
goto :eof

:remove-snapshot
echo Removing SNAPSHOT from version...
powershell -Command "(Get-Content pom.xml) -replace '-SNAPSHOT', '' | Set-Content pom.xml"
echo SNAPSHOT removed from version
goto :eof

:add-snapshot
echo Adding SNAPSHOT to version...
powershell -Command "(Get-Content pom.xml) -replace '<version>([0-9.]+)</version>', '<version>$1-SNAPSHOT</version>' | Set-Content pom.xml"
echo SNAPSHOT added to version
goto :eof

:tag-release
echo Creating release tag...
git tag -a release-%CLEAN_VERSION% -m "Release version %CLEAN_VERSION%"
echo Tag release-%CLEAN_VERSION% created. Push with: git push origin release-%CLEAN_VERSION%
goto :eof

:help
echo Available targets:
echo.
echo Build targets:
echo   all           - Development build (default)
echo   dev-build     - Development build (clean, compile, test, package)
echo   release-build - Full release build with all tests and tagging
echo   release       - Alias for release-build
echo.
echo Core Maven tasks:
echo   compile       - Compile source code
echo   verify        - Verify dependencies
echo   test          - Run unit tests
echo   package       - Build JAR package
echo.
echo Application runners:
echo   server        - Start server from source code
echo   client        - Start client from source code
echo.
echo Server management:
echo   StartServer     - Start reference server from .libs/
echo   StartOwnServer  - Start own server from target/
echo   StopServer      - Stop any server on configured port
echo.
echo Testing:
echo   test-reference  - Run acceptance tests against reference server
echo   test-own        - Run acceptance tests against own server
echo.
echo Version management:
echo   version         - Show version information
echo   bump-version    - Increment version number
echo   remove-snapshot - Remove SNAPSHOT from version
echo   add-snapshot    - Add SNAPSHOT to version
echo   tag-release     - Create Git release tag
echo.
echo Utilities:
echo   clean           - Remove build artifacts
echo   help            - Show this help message
echo.
echo Configuration:
echo   PORT           = %PORT%
echo   LIBS_DIR       = %LIBS_DIR%
echo   SERVER_JAR     = %SERVER_JAR%
echo   OWN_SERVER_JAR = %OWN_SERVER_JAR%
echo   VERSION        = %VERSION%
echo   NEXT_VERSION   = %NEXT_VERSION%
goto :eof
