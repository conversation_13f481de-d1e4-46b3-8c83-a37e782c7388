image: maven:3.9.6-eclipse-temurin-21

stages:
  - build
  - test
  - package
  - deploy

variables:
  PORT: "5050"  # Matches Dockerfile EXPOSE
  IMAGE_TAG: $CI_REGISTRY_IMAGE:$CI_COMMIT_REF_SLUG
  MAVEN_OPTS: "-Dhttps.protocols=TLSv1.2 -Dmaven.repo.local=$CI_PROJECT_DIR/.m2/repository"

cache:
  paths:
    - .m2/repository/
    - target/

build:
  stage: build
  script:
    - mvn clean package -DskipTests
  artifacts:
    paths:
      - target/*.jar
    expire_in: 1 week

unit-tests:
  stage: test
  script:
    - mvn test

acceptance-tests:
  stage: test
  script:
    - SERVER_JAR="target/robot-world-*-jar-with-dependencies.jar"
    - if [ ! -f $SERVER_JAR ]; then echo "Server JAR not found"; exit 1; fi
    - java -jar $SERVER_JAR $PORT &
    - sleep 5
    - mvn verify
    - kill $(lsof -t -i:$PORT) || true

docker-package:
  stage: package
  image: docker:24.0
  services:
    - docker:24.0-dind
  variables:
    DOCKER_HOST: tcp://docker:2376
    DOCKER_TLS_CERTDIR: "/certs"
  script:
    - docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $CI_REGISTRY
    - docker build -t $IMAGE_TAG .
    - docker push $IMAGE_TAG
  dependencies:
    - build
  only:
    - main
    - merge_requests

deploy:
  stage: deploy
  image: alpine/curl
  script:
    - echo "Deploying $IMAGE_TAG to production environment"
    # Add your deployment commands here
    # Example: curl -X POST "${DEPLOY_HOOK_URL}?image=${IMAGE_TAG}"
  needs:
    - docker-package
  only:
    - main